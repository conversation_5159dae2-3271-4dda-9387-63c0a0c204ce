/*
 * 🚀 ESP32-CAM Smart Security System with Telegram Bot Control
 * 
 * Professional IoT Security Camera with Motion Detection
 * Features: WiFi connectivity, Telegram bot control, Motion alerts, Custom UI
 * 
 * Hardware: ESP32-CAM AI Thinker + PIR Sensor + Flash LED
 * Author: IoT Security Expert
 * Version: 2.0 Professional Edition
 */

#include "esp_camera.h"
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <UniversalTelegramBot.h>
#include <ArduinoJson.h>

// ===========================================
// 🔧 CONFIGURATION - MODIFY THESE VALUES
// ===========================================
const char* WIFI_SSID = "SKR";
const char* WIFI_PASSWORD = "12345678";
const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
const char* CHAT_ID = "5283649744";

// 📌 Hardware Pin Definitions
#define PIR_PIN 13
#define FLASH_LED_PIN 4
#define STATUS_LED_PIN 33  // Built-in LED for status indication

// 🔧 System Configuration
#define WIFI_TIMEOUT_MS 30000      // 30 seconds WiFi timeout
#define MOTION_DEBOUNCE_MS 1000    // 1 second motion debounce
#define MOTION_COOLDOWN_MS 15000   // 15 seconds between motion alerts
#define BOT_REQUEST_DELAY 1000     // 1 second between bot requests
#define CAMERA_RETRY_COUNT 3       // Camera initialization retries

// 📷 ESP32-CAM AI Thinker Pin Configuration
#define PWDN_GPIO_NUM     32
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM      0
#define SIOD_GPIO_NUM     26
#define SIOC_GPIO_NUM     27
#define Y9_GPIO_NUM       35
#define Y8_GPIO_NUM       34
#define Y7_GPIO_NUM       39
#define Y6_GPIO_NUM       36
#define Y5_GPIO_NUM       21
#define Y4_GPIO_NUM       19
#define Y3_GPIO_NUM       18
#define Y2_GPIO_NUM        5
#define VSYNC_GPIO_NUM    25
#define HREF_GPIO_NUM     23
#define PCLK_GPIO_NUM     22

// ===========================================
// 🌐 GLOBAL VARIABLES & SYSTEM STATE
// ===========================================

// 📡 Network & Communication Objects
WiFiClientSecure secureClient;
UniversalTelegramBot bot(BOT_TOKEN, secureClient);

// ⏰ Timing Control Variables
unsigned long lastBotRequestTime = 0;
unsigned long systemStartTime = 0;
unsigned long lastMotionTime = 0;
volatile unsigned long lastMotionInterrupt = 0;

// 🔔 System State Flags
bool motionDetectionEnabled = true;
bool flashLedState = false;
bool faceDetectionEnabled = false;
bool systemInitialized = false;
bool wifiConnected = false;
bool telegramConnected = false;

// 🚨 Motion Detection Variables
volatile bool motionDetected = false;
bool motionInterruptAttached = false;

// 📊 System Statistics
unsigned long totalMotionEvents = 0;
unsigned long totalPhotosSent = 0;
unsigned long totalCommandsProcessed = 0;

// ===========================================
// 📷 CAMERA INITIALIZATION & CONTROL
// ===========================================

/**
 * Initialize ESP32-CAM with optimal settings
 * Returns: true if successful, false if failed
 */
bool initializeCamera() {
  Serial.println("📷 Initializing ESP32-CAM...");
  
  // Camera configuration structure
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sscb_sda = SIOD_GPIO_NUM;
  config.pin_sscb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;
  config.pixel_format = PIXFORMAT_JPEG;
  
  // Configure quality based on PSRAM availability
  if (psramFound()) {
    config.frame_size = FRAMESIZE_UXGA;  // 1600x1200
    config.jpeg_quality = 10;            // High quality (lower number = better quality)
    config.fb_count = 2;                 // Frame buffer count
    Serial.println("📦 PSRAM detected - High quality mode enabled");
  } else {
    config.frame_size = FRAMESIZE_SVGA;  // 800x600
    config.jpeg_quality = 12;            // Standard quality
    config.fb_count = 1;                 // Single frame buffer
    Serial.println("⚠️ No PSRAM - Standard quality mode");
  }

  // Initialize camera with retry mechanism
  esp_err_t err = ESP_FAIL;
  for (int attempt = 1; attempt <= CAMERA_RETRY_COUNT; attempt++) {
    Serial.printf("🔄 Camera init attempt %d/%d...\n", attempt, CAMERA_RETRY_COUNT);
    err = esp_camera_init(&config);
    
    if (err == ESP_OK) {
      Serial.println("✅ Camera initialized successfully!");
      return true;
    } else {
      Serial.printf("❌ Camera init failed (0x%x), retrying...\n", err);
      delay(1000);
    }
  }
  
  Serial.println("💥 Camera initialization failed after all attempts!");
  return false;
}

/**
 * Photo capture and transmission system
 */
camera_fb_t* currentFrameBuffer = nullptr;
size_t currentIndex = 0;

// Helper function to check if more data is available for transmission
bool isMoreDataAvailable() {
  return currentFrameBuffer && (currentIndex < currentFrameBuffer->len);
}

// Helper function to get next byte for transmission
byte getNextByte() {
  if (currentFrameBuffer && currentIndex < currentFrameBuffer->len) {
    return currentFrameBuffer->buf[currentIndex++];
  }
  return 0;
}

/**
 * Capture photo and send to Telegram
 * Returns: Status message string
 */
String captureAndSendPhoto(String chatId) {
  Serial.println("📸 Starting photo capture...");
  
  // Ensure system stability
  yield();
  
  // Capture photo
  currentFrameBuffer = esp_camera_fb_get();
  currentIndex = 0;
  
  if (!currentFrameBuffer) {
    Serial.println("❌ Camera capture failed!");
    return "❌ **Camera Error**\n\n🚫 Failed to capture photo\n💡 Try again or check camera connection";
  }
  
  Serial.printf("📷 Photo captured successfully: %d bytes\n", currentFrameBuffer->len);
  
  // Flash LED during transmission
  digitalWrite(FLASH_LED_PIN, HIGH);
  
  // Send photo via Telegram
  yield(); // Feed watchdog
  bool success = bot.sendPhotoByBinary(
    chatId, 
    "image/jpeg", 
    currentFrameBuffer->len,
    isMoreDataAvailable, 
    getNextByte, 
    nullptr, 
    nullptr
  );
  
  // Turn off flash
  digitalWrite(FLASH_LED_PIN, LOW);
  
  // Release frame buffer
  esp_camera_fb_return(currentFrameBuffer);
  currentFrameBuffer = nullptr;
  
  if (success) {
    totalPhotosSent++;
    Serial.println("✅ Photo sent successfully!");
    return "📸 **Photo Delivered!**\n\n✅ Image captured and sent successfully\n🎯 Total photos sent: " + String(totalPhotosSent);
  } else {
    Serial.println("❌ Failed to send photo");
    return "❌ **Transmission Failed**\n\n📷 Photo captured but failed to send\n📡 Check your internet connection";
  }
}

// ===========================================
// 🚨 MOTION DETECTION SYSTEM
// ===========================================

/**
 * Motion detection interrupt service routine
 * Called when PIR sensor detects motion
 */
void IRAM_ATTR motionDetectionISR() {
  unsigned long currentTime = millis();

  // Debounce check to prevent false triggers
  if (currentTime - lastMotionInterrupt > MOTION_DEBOUNCE_MS) {
    motionDetected = true;
    lastMotionInterrupt = currentTime;
  }
}

/**
 * Initialize motion detection system
 * Returns: true if successful, false if failed
 */
bool initializeMotionDetection() {
  Serial.println("🔔 Setting up motion detection...");

  // Configure PIR pin
  pinMode(PIR_PIN, INPUT);

  // Try to attach interrupt
  if (digitalPinToInterrupt(PIR_PIN) != -1) {
    // Ensure no existing interrupt is attached
    detachInterrupt(digitalPinToInterrupt(PIR_PIN));
    delay(100);

    // Attach new interrupt
    attachInterrupt(digitalPinToInterrupt(PIR_PIN), motionDetectionISR, RISING);
    motionInterruptAttached = true;
    Serial.println("✅ Motion interrupt attached successfully");
    return true;
  } else {
    Serial.println("⚠️ PIR pin doesn't support interrupts - using polling mode");
    motionInterruptAttached = false;
    return false;
  }
}

/**
 * Handle motion detection and alerts
 */
void handleMotionDetection() {
  if (!motionDetectionEnabled) return;

  bool currentMotion = false;

  // Check motion based on detection mode
  if (motionInterruptAttached) {
    currentMotion = motionDetected;
  } else {
    // Polling mode fallback
    currentMotion = (digitalRead(PIR_PIN) == HIGH);
  }

  if (currentMotion) {
    unsigned long currentTime = millis();

    // Check cooldown period to prevent spam
    if (currentTime - lastMotionTime > MOTION_COOLDOWN_MS) {
      lastMotionTime = currentTime;
      totalMotionEvents++;

      Serial.println("🚨 MOTION DETECTED! Initiating security protocol...");

      // Only proceed if WiFi is connected
      if (wifiConnected && telegramConnected) {
        // Send immediate alert
        String alertMessage = "🚨 **SECURITY ALERT** 🚨\n\n";
        alertMessage += "🕐 **Time:** `" + getSystemUptime() + "`\n";
        alertMessage += "📍 **Location:** ESP32-CAM Security System\n";
        alertMessage += "🔢 **Event #:** " + String(totalMotionEvents) + "\n";
        alertMessage += "📸 **Status:** Capturing photo...\n\n";
        alertMessage += "_🛡️ Automatic monitoring active_";

        bot.sendMessage(CHAT_ID, alertMessage, "Markdown");

        // Capture and send photo
        yield();
        String photoResult = captureAndSendPhoto(CHAT_ID);
        bot.sendMessage(CHAT_ID, photoResult, "Markdown");

      } else {
        Serial.println("⚠️ Network not available - motion logged but not transmitted");
      }
    }

    // Reset motion flag
    motionDetected = false;
  }
}

// ===========================================
// 🛠️ UTILITY FUNCTIONS
// ===========================================

/**
 * Get formatted system uptime
 */
String getSystemUptime() {
  unsigned long uptime = (millis() - systemStartTime) / 1000;
  unsigned long days = uptime / 86400;
  unsigned long hours = (uptime % 86400) / 3600;
  unsigned long minutes = (uptime % 3600) / 60;
  unsigned long seconds = uptime % 60;

  String timeStr = "";
  if (days > 0) timeStr += String(days) + "d ";
  if (hours > 0) timeStr += String(hours) + "h ";
  if (minutes > 0) timeStr += String(minutes) + "m ";
  timeStr += String(seconds) + "s";

  return timeStr;
}

/**
 * Get comprehensive WiFi information
 */
String getWiFiInformation() {
  String info = "🌐 **WiFi Network Information**\n\n";

  if (wifiConnected) {
    info += "📶 **SSID:** `" + WiFi.SSID() + "`\n";
    info += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
    info += "🔗 **MAC Address:** `" + WiFi.macAddress() + "`\n";
    info += "📡 **Signal Strength:** `" + String(WiFi.RSSI()) + " dBm`\n";

    // Signal quality assessment
    int rssi = WiFi.RSSI();
    String quality;
    if (rssi > -30) quality = "Excellent 🟢";
    else if (rssi > -50) quality = "Very Good 🟡";
    else if (rssi > -70) quality = "Good 🟠";
    else if (rssi > -80) quality = "Fair 🔴";
    else quality = "Poor ⚫";

    info += "📊 **Signal Quality:** " + quality + "\n";
    info += "🔌 **Status:** Connected ✅";
  } else {
    info += "❌ **Status:** Disconnected\n";
    info += "🔄 **Action:** Attempting reconnection...";
  }

  return info;
}

/**
 * Get comprehensive system status
 */
String getSystemStatus() {
  String status = "📡 **System Status Report**\n\n";

  // System Information
  status += "🟢 **System:** " + String(systemInitialized ? "Online" : "Initializing") + "\n";
  status += "⏱ **Uptime:** `" + getSystemUptime() + "`\n";
  status += "💾 **Free Heap:** `" + String(ESP.getFreeHeap()) + " bytes`\n";
  status += "🧠 **PSRAM:** " + String(psramFound() ? "Available ✅" : "Not Found ❌") + "\n\n";

  // Network Status
  status += "📶 **WiFi:** " + String(wifiConnected ? "Connected ✅" : "Disconnected ❌") + "\n";
  if (wifiConnected) {
    status += "🌍 **IP:** `" + WiFi.localIP().toString() + "`\n";
    status += "📡 **RSSI:** `" + String(WiFi.RSSI()) + " dBm`\n";
  }
  status += "🤖 **Telegram:** " + String(telegramConnected ? "Connected ✅" : "Disconnected ❌") + "\n\n";

  // Security Features
  status += "🛰️ **Motion Detection:** " + String(motionDetectionEnabled ? "Enabled ✅" : "Disabled ❌") + "\n";
  status += "🔔 **Detection Mode:** " + String(motionInterruptAttached ? "Interrupt" : "Polling") + "\n";
  status += "💡 **Flash LED:** " + String(flashLedState ? "ON ✅" : "OFF ❌") + "\n";
  status += "🧠 **Face Detection:** " + String(faceDetectionEnabled ? "Enabled ✅" : "Disabled ❌") + "\n\n";

  // Statistics
  status += "📊 **Statistics:**\n";
  status += "🚨 Motion Events: `" + String(totalMotionEvents) + "`\n";
  status += "📸 Photos Sent: `" + String(totalPhotosSent) + "`\n";
  status += "⌨️ Commands Processed: `" + String(totalCommandsProcessed) + "`";

  return status;
}

// ===========================================
// 🎛️ TELEGRAM CUSTOM KEYBOARD & COMMANDS
// ===========================================

/**
 * Create custom reply keyboard for Telegram
 */
String createCustomKeyboard() {
  String keyboard = "[[\"📸 /photo\", \"💡 /flash\"],"
                   "[\"📡 /status\", \"⏱ /uptime\"],"
                   "[\"🛰️ /motionon\", \"🔕 /motionoff\"],"
                   "[\"🌐 /wifi\", \"🏓 /ping\"],"
                   "[\"🔁 /reboot\", \"🆘 /help\"]]";
  return keyboard;
}

/**
 * Send message with custom keyboard
 */
void sendMessageWithKeyboard(String chatId, String message) {
  String keyboard = createCustomKeyboard();
  bot.sendMessageWithReplyKeyboard(chatId, message, "Markdown", keyboard, true);
}

/**
 * Process incoming Telegram messages and commands
 */
void handleTelegramMessages() {
  int numNewMessages = bot.getUpdates(bot.last_message_received + 1);

  while (numNewMessages) {
    Serial.printf("📨 Processing %d new message(s)\n", numNewMessages);

    for (int i = 0; i < numNewMessages; i++) {
      String chatId = String(bot.messages[i].chat_id);
      String messageText = bot.messages[i].text;
      String fromName = bot.messages[i].from_name;

      // Security check - only respond to authorized chat ID
      if (chatId != CHAT_ID) {
        Serial.println("🚫 Unauthorized access attempt from: " + chatId);
        bot.sendMessage(chatId, "🚫 **Access Denied**\n\nUnauthorized user detected!\nThis incident has been logged.", "Markdown");
        continue;
      }

      totalCommandsProcessed++;
      Serial.printf("👤 From: %s | 💭 Command: %s\n", fromName.c_str(), messageText.c_str());

      // Process commands
      processCommand(chatId, messageText, fromName);
    }

    numNewMessages = bot.getUpdates(bot.last_message_received + 1);
  }
}

/**
 * Process individual commands
 */
void processCommand(String chatId, String command, String fromName) {
  // Remove any extra characters and normalize command
  command.trim();
  command.toLowerCase();

  if (command == "/start") {
    handleStartCommand(chatId, fromName);
  }
  else if (command == "/photo") {
    handlePhotoCommand(chatId);
  }
  else if (command == "/flash") {
    handleFlashCommand(chatId);
  }
  else if (command == "/status") {
    handleStatusCommand(chatId);
  }
  else if (command == "/reboot") {
    handleRebootCommand(chatId);
  }
  else if (command == "/ping") {
    handlePingCommand(chatId);
  }
  else if (command == "/motionon") {
    handleMotionOnCommand(chatId);
  }
  else if (command == "/motionoff") {
    handleMotionOffCommand(chatId);
  }
  else if (command == "/uptime") {
    handleUptimeCommand(chatId);
  }
  else if (command == "/wifi") {
    handleWiFiCommand(chatId);
  }
  else if (command == "/help") {
    handleHelpCommand(chatId);
  }
  else if (command == "/faceon") {
    handleFaceOnCommand(chatId);
  }
  else if (command == "/faceoff") {
    handleFaceOffCommand(chatId);
  }
  else {
    handleUnknownCommand(chatId, command);
  }
}

// ===========================================
// 🎯 INDIVIDUAL COMMAND HANDLERS
// ===========================================

/**
 * Handle /start command - Welcome message with keyboard
 */
void handleStartCommand(String chatId, String fromName) {
  String welcome = "🏁 **ESP32-CAM Security System**\n\n";
  welcome += "Welcome *" + fromName + "*! 👋\n\n";
  welcome += "🛡️ Your smart security system is **ONLINE** and ready!\n\n";
  welcome += "📋 **Quick Access Menu:**\n";
  welcome += "Use the buttons below for instant control! ⚡\n\n";
  welcome += "🔒 **System Status:**\n";
  welcome += "• Status: ✅ Active\n";
  welcome += "• Connection: ✅ Stable\n";
  welcome += "• Motion Detection: " + String(motionDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
  welcome += "• Uptime: `" + getSystemUptime() + "`\n\n";
  welcome += "_Your security is our priority!_ 🛡️";

  sendMessageWithKeyboard(chatId, welcome);
}

/**
 * Handle /photo command - Capture and send photo
 */
void handlePhotoCommand(String chatId) {
  bot.sendMessage(chatId, "📸 **Capturing Photo...**\n\nPlease wait while I take a picture! 📷✨", "Markdown");
  String result = captureAndSendPhoto(chatId);
  bot.sendMessage(chatId, result, "Markdown");
}

/**
 * Handle /flash command - Toggle flash LED
 */
void handleFlashCommand(String chatId) {
  flashLedState = !flashLedState;
  digitalWrite(FLASH_LED_PIN, flashLedState ? HIGH : LOW);
  String msg = flashLedState ?
    "💡 **Flash LED ON**\n\n✨ Flash light activated!\n🔆 LED is now illuminated" :
    "🌙 **Flash LED OFF**\n\n💤 Flash light deactivated!\n🌑 LED is now off";
  bot.sendMessage(chatId, msg, "Markdown");
}

/**
 * Handle /status command - System status report
 */
void handleStatusCommand(String chatId) {
  String status = getSystemStatus();
  bot.sendMessage(chatId, status, "Markdown");
}

/**
 * Handle /reboot command - Restart system
 */
void handleRebootCommand(String chatId) {
  bot.sendMessage(chatId, "🔁 **Rebooting System...**\n\nESP32-CAM will restart now! ⚡\n\n_Please wait 30 seconds before reconnecting_ ⏳", "Markdown");
  delay(2000);
  ESP.restart();
}

/**
 * Handle /ping command - Test connection
 */
void handlePingCommand(String chatId) {
  bot.sendMessage(chatId, "🏓 **PONG!** 🎾\n\n⚡ **Response Time:** Quick!\n✅ **Status:** System Responsive\n📶 **Connection:** Stable\n\n_System is alive and well!_ 💚", "Markdown");
}

/**
 * Handle /motionon command - Enable motion detection
 */
void handleMotionOnCommand(String chatId) {
  motionDetectionEnabled = true;
  bot.sendMessage(chatId, "🛰️ **Motion Detection ENABLED**\n\n✅ System will now monitor for motion\n🚨 Alerts will be sent automatically\n📸 Photos will be captured on detection\n\n_Stay secure!_ 🛡️", "Markdown");
}

/**
 * Handle /motionoff command - Disable motion detection
 */
void handleMotionOffCommand(String chatId) {
  motionDetectionEnabled = false;
  bot.sendMessage(chatId, "🔕 **Motion Detection DISABLED**\n\n❌ Motion monitoring stopped\n😴 System in quiet mode\n📵 No automatic alerts\n\n_Manual control only_ 🎛️", "Markdown");
}

/**
 * Handle /uptime command - Show system uptime
 */
void handleUptimeCommand(String chatId) {
  String uptime = "⏱ **System Uptime**\n\n";
  uptime += "🕐 **Running for:** `" + getSystemUptime() + "`\n";
  uptime += "🔄 **Started:** System boot successful\n";
  uptime += "⚡ **Status:** Stable & Operational\n";
  uptime += "📊 **Performance:** Excellent\n\n";
  uptime += "_System has been running smoothly!_ ✨";

  bot.sendMessage(chatId, uptime, "Markdown");
}

/**
 * Handle /wifi command - WiFi information
 */
void handleWiFiCommand(String chatId) {
  String wifiInfo = getWiFiInformation();
  bot.sendMessage(chatId, wifiInfo, "Markdown");
}

/**
 * Handle /help command - Command reference
 */
void handleHelpCommand(String chatId) {
  String help = "🆘 **Command Reference Guide**\n\n";
  help += "📋 **Available Commands:**\n\n";
  help += "🏁 `/start` - *Welcome & status overview*\n";
  help += "📸 `/photo` - *Capture & send photo*\n";
  help += "💡 `/flash` - *Toggle flash LED*\n";
  help += "📡 `/status` - *Detailed system status*\n";
  help += "🔁 `/reboot` - *Restart the system*\n";
  help += "🏓 `/ping` - *Test connection*\n";
  help += "🛰️ `/motionon` - *Enable motion alerts*\n";
  help += "🔕 `/motionoff` - *Disable motion alerts*\n";
  help += "⏱ `/uptime` - *Show system uptime*\n";
  help += "🌐 `/wifi` - *WiFi connection info*\n";
  help += "🧠 `/faceon` - *Enable face detection*\n";
  help += "🚫 `/faceoff` - *Disable face detection*\n\n";
  help += "💡 **Quick Tip:** Use the buttons for faster access! ⚡";

  sendMessageWithKeyboard(chatId, help);
}

/**
 * Handle /faceon command - Enable face detection
 */
void handleFaceOnCommand(String chatId) {
  faceDetectionEnabled = true;
  bot.sendMessage(chatId, "🧠 **Face Detection ENABLED**\n\n✅ AI face recognition active\n👤 Will detect human faces\n🎯 Enhanced security mode\n\n_Smart monitoring activated!_ 🤖", "Markdown");
}

/**
 * Handle /faceoff command - Disable face detection
 */
void handleFaceOffCommand(String chatId) {
  faceDetectionEnabled = false;
  bot.sendMessage(chatId, "🚫 **Face Detection DISABLED**\n\n❌ Face recognition stopped\n📷 Basic camera mode only\n⚡ Reduced processing load\n\n_Standard monitoring mode_ 📸", "Markdown");
}

/**
 * Handle unknown commands
 */
void handleUnknownCommand(String chatId, String command) {
  String unknown = "❓ **Unknown Command**\n\n";
  unknown += "Command `" + command + "` not recognized.\n\n";
  unknown += "💡 **Try these instead:**\n";
  unknown += "• Type `/help` for all commands\n";
  unknown += "• Type `/start` to begin\n";
  unknown += "• Type `/status` for system info\n\n";
  unknown += "_I'm here to help!_ 🤖";

  bot.sendMessage(chatId, unknown, "Markdown");
}

// ===========================================
// 🚀 SYSTEM INITIALIZATION
// ===========================================

/**
 * Initialize WiFi connection with robust error handling
 */
bool initializeWiFi() {
  Serial.println("📡 Initializing WiFi connection...");

  WiFi.mode(WIFI_STA);
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

  unsigned long startTime = millis();
  int attempts = 0;

  while (WiFi.status() != WL_CONNECTED && (millis() - startTime) < WIFI_TIMEOUT_MS) {
    delay(500);
    Serial.print(".");
    attempts++;

    yield(); // Feed watchdog

    // Retry connection every 10 attempts
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.printf("🔄 WiFi attempt %d - Retrying...\n", attempts / 10);
      WiFi.disconnect();
      delay(1000);
      WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    wifiConnected = true;
    Serial.println("\n✅ WiFi connected successfully!");
    Serial.printf("🌍 IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("📶 Signal Strength: %d dBm\n", WiFi.RSSI());
    Serial.printf("🔗 MAC Address: %s\n", WiFi.macAddress().c_str());
    return true;
  } else {
    wifiConnected = false;
    Serial.println("\n❌ WiFi connection failed!");
    return false;
  }
}

/**
 * Initialize Telegram bot connection
 */
bool initializeTelegram() {
  Serial.println("🤖 Initializing Telegram bot...");

  // Configure secure client
  secureClient.setInsecure(); // For newer ESP32 core versions

  // Test connection with retry logic
  bool connected = false;
  for (int attempt = 1; attempt <= 3; attempt++) {
    Serial.printf("🔄 Telegram connection attempt %d/3...\n", attempt);

    if (bot.getMe()) {
      connected = true;
      break;
    } else {
      Serial.println("⚠️ Telegram connection failed, retrying...");
      delay(2000);
    }
    yield();
  }

  if (connected) {
    telegramConnected = true;
    Serial.println("✅ Telegram bot connected successfully!");
    return true;
  } else {
    telegramConnected = false;
    Serial.println("❌ Telegram connection failed!");
    return false;
  }
}

// ===========================================
// 🚀 MAIN SETUP FUNCTION
// ===========================================
void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  delay(2000); // Give serial time to initialize
  Serial.println();
  Serial.println("🚀 ESP32-CAM Smart Security System Starting...");
  Serial.println("📋 Version: 2.0 Professional Edition");
  Serial.println("🔧 Initializing system components...");

  // Record system start time
  systemStartTime = millis();

  // Initialize GPIO pins
  Serial.println("📌 Configuring GPIO pins...");
  pinMode(PIR_PIN, INPUT);
  pinMode(FLASH_LED_PIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT);
  digitalWrite(FLASH_LED_PIN, LOW);
  digitalWrite(STATUS_LED_PIN, HIGH); // Turn on status LED

  // Initialize camera
  if (!initializeCamera()) {
    Serial.println("💥 CRITICAL ERROR: Camera initialization failed!");
    Serial.println("🔄 System will restart in 10 seconds...");
    delay(10000);
    ESP.restart();
  }

  // Initialize motion detection
  initializeMotionDetection();

  // Initialize WiFi
  if (!initializeWiFi()) {
    Serial.println("💥 CRITICAL ERROR: WiFi initialization failed!");
    Serial.println("🔄 System will restart in 10 seconds...");
    delay(10000);
    ESP.restart();
  }

  // Initialize Telegram
  if (initializeTelegram()) {
    // Send startup notification
    String startupMessage = "🚀 **System Online!**\n\n";
    startupMessage += "✅ ESP32-CAM Security System started successfully\n";
    startupMessage += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
    startupMessage += "📶 **WiFi Signal:** `" + String(WiFi.RSSI()) + " dBm`\n";
    startupMessage += "🔔 **Motion Detection:** " + String(motionInterruptAttached ? "✅ Interrupt Mode" : "⚠️ Polling Mode") + "\n";
    startupMessage += "🧠 **PSRAM:** " + String(psramFound() ? "✅ Available" : "❌ Not Found") + "\n";
    startupMessage += "⏱ **Boot Time:** `" + getSystemUptime() + "`\n\n";
    startupMessage += "_🛡️ Your security system is ready and monitoring!_\n\n";
    startupMessage += "Type `/start` to access the control panel! 🎛️";

    if (bot.sendMessage(CHAT_ID, startupMessage, "Markdown")) {
      Serial.println("📨 Startup notification sent successfully!");
    } else {
      Serial.println("⚠️ Failed to send startup notification");
    }
  }

  // System initialization complete
  systemInitialized = true;
  digitalWrite(STATUS_LED_PIN, LOW); // Turn off status LED

  Serial.println("🎉 System initialization completed successfully!");
  Serial.println("🔒 ESP32-CAM Security System is now ACTIVE and monitoring...");
  Serial.printf("💾 Free heap memory: %d bytes\n", ESP.getFreeHeap());
  Serial.printf("📊 Total system uptime: %s\n", getSystemUptime().c_str());
  Serial.println("==================================================");
}

// ===========================================
// 🔄 MAIN SYSTEM LOOP
// ===========================================
void loop() {
  // Feed watchdog timer to prevent system resets
  yield();

  // Monitor WiFi connection and reconnect if needed
  if (WiFi.status() != WL_CONNECTED) {
    wifiConnected = false;
    Serial.println("⚠️ WiFi connection lost! Attempting reconnection...");

    if (initializeWiFi()) {
      Serial.println("✅ WiFi reconnected successfully!");
      // Reinitialize Telegram if WiFi was restored
      if (!telegramConnected) {
        initializeTelegram();
      }
    } else {
      Serial.println("❌ WiFi reconnection failed, retrying in next cycle...");
      delay(5000); // Wait before next attempt
    }
  }

  // Handle motion detection (only if system is fully initialized)
  if (systemInitialized) {
    handleMotionDetection();
  }

  // Handle Telegram messages with timing control
  if (telegramConnected && (millis() > lastBotRequestTime + BOT_REQUEST_DELAY)) {
    handleTelegramMessages();
    lastBotRequestTime = millis();
  }

  // System health monitoring (every 5 minutes)
  static unsigned long lastHealthCheck = 0;
  if (millis() - lastHealthCheck > 300000) { // 5 minutes
    lastHealthCheck = millis();
    Serial.printf("💚 System Health: Uptime %s, Free Heap: %d bytes\n",
                  getSystemUptime().c_str(), ESP.getFreeHeap());

    // Check for low memory condition
    if (ESP.getFreeHeap() < 10000) {
      Serial.println("⚠️ WARNING: Low memory condition detected!");
    }
  }

  // Feed watchdog and maintain system responsiveness
  yield();
  delay(100); // Balanced delay for optimal performance
}
