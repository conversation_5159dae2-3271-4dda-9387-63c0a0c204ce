# 🚀 Quick Setup Guide - ESP32-CAM Security System

## 📋 Pre-Setup Checklist

- [ ] ESP32-CAM AI Thinker module
- [ ] PIR motion sensor (HC-SR501)
- [ ] FTDI USB programmer
- [ ] 5V power supply (2A)
- [ ] Jumper wires
- [ ] Arduino IDE installed
- [ ] Telegram account

## 🔧 Step 1: Arduino IDE Setup

### Install Required Libraries
1. Open Arduino IDE
2. Go to **Tools → Manage Libraries**
3. Install these libraries:
   - `ESP32` by Espressif Systems
   - `UniversalTelegramBot` by <PERSON>
   - `<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>` by <PERSON><PERSON>

### Configure Board Settings
1. **File → Preferences**
2. Add ESP32 board URL: `https://dl.espressif.com/dl/package_esp32_index.json`
3. **Tools → Board → ESP32 Arduino → ESP32 Wrover Module**
4. Set these parameters:
   - Upload Speed: **115200**
   - Flash Frequency: **40MHz**
   - Flash Mode: **QIO**
   - Partition Scheme: **Huge APP (3MB No OTA/1MB SPIFFS)**

## 🤖 Step 2: Create Telegram Bot

### Get Bot Token
1. Open Telegram, search for `@BotFather`
2. Send `/newbot`
3. Choose bot name: `ESP32 Security Cam`
4. Choose username: `esp32_security_cam_bot`
5. **Copy the Bot Token** (save it!)

### Get Your Chat ID
1. Send a message to your new bot
2. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Find your **Chat ID** in the response
4. **Copy the Chat ID** (save it!)

## ⚙️ Step 3: Configure Code

### Update Configuration
Open the `.ino` file and modify these lines:

```cpp
const char* WIFI_SSID = "Your_WiFi_Name";        // ← Your WiFi name
const char* WIFI_PASSWORD = "Your_WiFi_Password"; // ← Your WiFi password
const char* BOT_TOKEN = "Your_Bot_Token_Here";    // ← From BotFather
const char* CHAT_ID = "Your_Chat_ID_Here";        // ← From getUpdates
```

## 🔌 Step 4: Hardware Connections

### PIR Sensor to ESP32-CAM
```
PIR VCC (Red)    → ESP32-CAM 5V
PIR GND (Black)  → ESP32-CAM GND
PIR OUT (Yellow) → ESP32-CAM GPIO13
```

### FTDI Programmer (for upload)
```
FTDI 5V  → ESP32-CAM 5V
FTDI GND → ESP32-CAM GND
FTDI TX  → ESP32-CAM U0R
FTDI RX  → ESP32-CAM U0T
```

## 📤 Step 5: Upload Code

### Programming Mode
1. Connect **GPIO0 to GND** (use jumper wire)
2. Press **RESET** button on ESP32-CAM
3. Click **Upload** in Arduino IDE
4. Wait for upload to complete
5. **Remove GPIO0-GND connection**
6. Press **RESET** button

### Verify Upload
- Open **Serial Monitor** (115200 baud)
- You should see startup messages
- Look for "System Online!" message

## ⚡ Step 6: Power & Test

### Switch to External Power
1. Disconnect FTDI programmer
2. Connect 5V power supply
3. System should boot automatically

### Test System
1. Open Telegram
2. Find your bot
3. Send `/start` command
4. You should see custom keyboard
5. Try `/photo` command
6. Test motion detection

## 🎯 Step 7: Final Setup

### PIR Sensor Adjustment
- **Sensitivity:** Adjust potentiometer for detection range
- **Time Delay:** Set how long sensor stays active
- **Position:** Mount 2-3 meters high, avoid direct sunlight

### System Verification
- [ ] WiFi connects successfully
- [ ] Telegram bot responds
- [ ] Camera captures photos
- [ ] Motion detection works
- [ ] All commands functional

## 🔧 Troubleshooting

### Common Issues & Solutions

**🚫 Boot Loop**
- Check power supply (must be 5V, 2A)
- Verify all connections
- Ensure GPIO0 not connected to GND during normal operation

**📡 WiFi Won't Connect**
- Verify SSID and password
- Check 2.4GHz network (ESP32 doesn't support 5GHz)
- Move closer to router

**📷 Camera Error**
- Check camera module connection
- Verify PSRAM detection in serial output
- Try different power supply

**🤖 Telegram Not Working**
- Verify bot token and chat ID
- Check internet connection
- Test with `/ping` command

**🚨 Motion Not Detected**
- Check PIR sensor wiring
- Adjust sensitivity potentiometer
- Verify GPIO13 connection

### Debug Commands
- `/status` - Check system health
- `/ping` - Test connectivity
- `/wifi` - Check network info
- `/uptime` - System runtime

## 📊 Expected Performance

### Boot Sequence (Normal)
```
🚀 ESP32-CAM Smart Security System Starting...
📷 Initializing ESP32-CAM...
📦 PSRAM detected - High quality mode enabled
✅ Camera initialized successfully!
🔔 Setting up motion detection...
✅ Motion interrupt attached successfully
📡 Initializing WiFi connection...
✅ WiFi connected successfully!
🤖 Initializing Telegram bot...
✅ Telegram bot connected successfully!
🎉 System initialization completed successfully!
```

### Typical Response Times
- **Boot Time:** 10-15 seconds
- **Photo Capture:** 2-5 seconds
- **Motion Response:** <1 second
- **Command Response:** 1-3 seconds

## 🎉 Success Indicators

✅ **System Working Properly When:**
- Startup notification received on Telegram
- `/start` shows custom keyboard
- `/photo` captures and sends image
- Motion triggers automatic alerts
- All commands respond correctly

## 📞 Need Help?

### Check These First:
1. Serial monitor output (115200 baud)
2. Power supply voltage and current
3. WiFi signal strength
4. Telegram bot token validity

### System Status:
- Use `/status` command for comprehensive system info
- Monitor serial output for error messages
- Check LED indicators on ESP32-CAM

---

**🎊 Congratulations! Your ESP32-CAM Security System is ready!** 🛡️

**Next Steps:**
- Mount in desired location
- Test motion detection range
- Set up monitoring schedule
- Enjoy your smart security system!
