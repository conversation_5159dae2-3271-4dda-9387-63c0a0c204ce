D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:57:13: error: conflicting declaration 'const char* TELEGRAM_CERTIFICATE_ROOT'
   57 | const char* TELEGRAM_CERTIFICATE_ROOT = \
      |             ^~~~~~~~~~~~~~~~~~~~~~~~~
In file included from c:\Users\<USER>\Documents\Arduino\libraries\UniversalTelegramBot\src/UniversalTelegramBot.h:31,
                 from D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:15:
c:\Users\<USER>\Documents\Arduino\libraries\UniversalTelegramBot\src/TelegramCertificate.h:9:12: note: previous declaration as 'const char TELEGRAM_CERTIFICATE_ROOT [1369]'
    9 | const char TELEGRAM_CERTIFICATE_ROOT[] = R"=EOF=(
      |            ^~~~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino: In function 'void processCommand(String, String, String)':
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:510:5: error: 'handleFlashCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  510 |     handleFlashCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:513:5: error: 'handleStatusCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  513 |     handleStatusCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:516:5: error: 'handleRebootCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  516 |     handleRebootCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:519:5: error: 'handlePingCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  519 |     handlePingCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:522:5: error: 'handleMotionOnCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  522 |     handleMotionOnCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:525:5: error: 'handleMotionOffCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  525 |     handleMotionOffCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:528:5: error: 'handleUptimeCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  528 |     handleUptimeCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:531:5: error: 'handleWiFiCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  531 |     handleWiFiCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:534:5: error: 'handleHelpCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  534 |     handleHelpCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:537:5: error: 'handleFaceOnCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  537 |     handleFaceOnCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:540:5: error: 'handleFaceOffCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  540 |     handleFaceOffCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:543:5: error: 'handleUnknownCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  543 |     handleUnknownCommand(chatId, command);
      |     ^~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino: At global scope:
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:578:9: error: 'bot' does not name a type; did you mean 'bit'?
  578 |         bot.sendMessage(chat_id, result, "Markdown");
      |         ^~~
      |         bit
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:579:7: error: expected declaration before '}' token
  579 |       }
      |       ^
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:581:7: error: expected unqualified-id before 'else'
  581 |       else if (text == "/flash") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:588:7: error: expected unqualified-id before 'else'
  588 |       else if (text == "/status") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:603:7: error: expected unqualified-id before 'else'
  603 |       else if (text == "/reboot") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:609:7: error: expected unqualified-id before 'else'
  609 |       else if (text == "/ping") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:613:7: error: expected unqualified-id before 'else'
  613 |       else if (text == "/motionon") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:618:7: error: expected unqualified-id before 'else'
  618 |       else if (text == "/motionoff") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:623:7: error: expected unqualified-id before 'else'
  623 |       else if (text == "/uptime") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:633:7: error: expected unqualified-id before 'else'
  633 |       else if (text == "/wifi") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:637:7: error: expected unqualified-id before 'else'
  637 |       else if (text == "/help") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:657:7: error: expected unqualified-id before 'else'
  657 |       else if (text == "/faceon") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:662:7: error: expected unqualified-id before 'else'
  662 |       else if (text == "/faceoff") {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:667:7: error: expected unqualified-id before 'else'
  667 |       else {
      |       ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:678:5: error: expected declaration before '}' token
  678 |     }
      |     ^
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:680:5: error: 'numNewMessages' does not name a type
  680 |     numNewMessages = bot.getUpdates(bot.last_message_received + 1);
      |     ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:681:3: error: expected declaration before '}' token
  681 |   }
      |   ^
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:682:1: error: expected declaration before '}' token
  682 | }
      | ^
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino: In function 'void setup()':
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:694:3: error: 'startTime' was not declared in this scope; did you mean 'strptime'?
  694 |   startTime = millis();
      |   ^~~~~~~~~
      |   strptime
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:709:53: error: 'motionISR' was not declared in this scope
  709 |     attachInterrupt(digitalPinToInterrupt(PIR_PIN), motionISR, RISING);
      |                                                     ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:710:5: error: 'interruptAttached' was not declared in this scope; did you mean 'motionInterruptAttached'?
  710 |     interruptAttached = true;
      |     ^~~~~~~~~~~~~~~~~
      |     motionInterruptAttached
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:714:5: error: 'interruptAttached' was not declared in this scope; did you mean 'motionInterruptAttached'?
  714 |     interruptAttached = false;
      |     ^~~~~~~~~~~~~~~~~
      |     motionInterruptAttached
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:719:8: error: 'initCamera' was not declared in this scope
  719 |   if (!initCamera()) {
      |        ^~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:766:3: error: 'clientTCP' was not declared in this scope; did you mean 'client_h'?
  766 |   clientTCP.setInsecure(); // Use this for newer ESP32 core versions
      |   ^~~~~~~~~
      |   client_h
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:793:51: error: 'interruptAttached' was not declared in this scope; did you mean 'motionInterruptAttached'?
  793 |     startupMsg += "🛰️ Motion Detection: " + String(interruptAttached ? "✅ Interrupt" : "⚠️ Polling") + "\n\n";
      |                                                   ^~~~~~~~~~~~~~~~~
      |                                                   motionInterruptAttached
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino: In function 'void loop()':
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:827:18: error: 'lastTimeBotRan' was not declared in this scope
  827 |   if (millis() > lastTimeBotRan + botRequestDelay) {
      |                  ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:827:35: error: 'botRequestDelay' was not declared in this scope
  827 |   if (millis() > lastTimeBotRan + botRequestDelay) {
      |                                   ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino: At global scope:
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:21:13: error: redefinition of 'const char* WIFI_SSID'
   21 | const char* WIFI_SSID = "SKR";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:21:13: note: 'const char* WIFI_SSID' previously defined here
   21 | const char* WIFI_SSID = "SKR";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:22:13: error: redefinition of 'const char* WIFI_PASSWORD'
   22 | const char* WIFI_PASSWORD = "12345678";
      |             ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:22:13: note: 'const char* WIFI_PASSWORD' previously defined here
   22 | const char* WIFI_PASSWORD = "12345678";
      |             ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:23:13: error: redefinition of 'const char* BOT_TOKEN'
   23 | const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:23:13: note: 'const char* BOT_TOKEN' previously defined here
   23 | const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:24:13: error: redefinition of 'const char* CHAT_ID'
   24 | const char* CHAT_ID = "5283649744";
      |             ^~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:24:13: note: 'const char* CHAT_ID' previously defined here
   24 | const char* CHAT_ID = "5283649744";
      |             ^~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:61:18: error: redefinition of 'WiFiClientSecure secureClient'
   61 | WiFiClientSecure secureClient;
      |                  ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:87:18: note: 'WiFiClientSecure secureClient' previously declared here
   87 | WiFiClientSecure secureClient;
      |                  ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:62:22: error: redefinition of 'UniversalTelegramBot bot'
   62 | UniversalTelegramBot bot(BOT_TOKEN, secureClient);
      |                      ^~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:88:22: note: 'UniversalTelegramBot bot' previously declared here
   88 | UniversalTelegramBot bot(BOT_TOKEN, secureClient);
      |                      ^~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:65:15: error: redefinition of 'long unsigned int lastBotRequestTime'
   65 | unsigned long lastBotRequestTime = 0;
      |               ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:91:15: note: 'long unsigned int lastBotRequestTime' previously defined here
   91 | unsigned long lastBotRequestTime = 0;
      |               ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:66:15: error: redefinition of 'long unsigned int systemStartTime'
   66 | unsigned long systemStartTime = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:92:15: note: 'long unsigned int systemStartTime' previously defined here
   92 | unsigned long systemStartTime = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:67:15: error: redefinition of 'long unsigned int lastMotionTime'
   67 | unsigned long lastMotionTime = 0;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:93:15: note: 'long unsigned int lastMotionTime' previously defined here
   93 | unsigned long lastMotionTime = 0;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:68:24: error: redefinition of 'volatile long unsigned int lastMotionInterrupt'
   68 | volatile unsigned long lastMotionInterrupt = 0;
      |                        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:94:24: note: 'volatile long unsigned int lastMotionInterrupt' previously defined here
   94 | volatile unsigned long lastMotionInterrupt = 0;
      |                        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:71:6: error: redefinition of 'bool motionDetectionEnabled'
   71 | bool motionDetectionEnabled = true;
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:97:6: note: 'bool motionDetectionEnabled' previously defined here
   97 | bool motionDetectionEnabled = true;
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:72:6: error: redefinition of 'bool flashLedState'
   72 | bool flashLedState = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:98:6: note: 'bool flashLedState' previously defined here
   98 | bool flashLedState = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:73:6: error: redefinition of 'bool faceDetectionEnabled'
   73 | bool faceDetectionEnabled = false;
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:99:6: note: 'bool faceDetectionEnabled' previously defined here
   99 | bool faceDetectionEnabled = false;
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:74:6: error: redefinition of 'bool systemInitialized'
   74 | bool systemInitialized = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:100:6: note: 'bool systemInitialized' previously defined here
  100 | bool systemInitialized = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:75:6: error: redefinition of 'bool wifiConnected'
   75 | bool wifiConnected = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:101:6: note: 'bool wifiConnected' previously defined here
  101 | bool wifiConnected = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:76:6: error: redefinition of 'bool telegramConnected'
   76 | bool telegramConnected = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:102:6: note: 'bool telegramConnected' previously defined here
  102 | bool telegramConnected = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:79:15: error: redefinition of 'volatile bool motionDetected'
   79 | volatile bool motionDetected = false;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:105:15: note: 'volatile bool motionDetected' previously defined here
  105 | volatile bool motionDetected = false;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:80:6: error: redefinition of 'bool motionInterruptAttached'
   80 | bool motionInterruptAttached = false;
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:106:6: note: 'bool motionInterruptAttached' previously defined here
  106 | bool motionInterruptAttached = false;
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:83:15: error: redefinition of 'long unsigned int totalMotionEvents'
   83 | unsigned long totalMotionEvents = 0;
      |               ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:109:15: note: 'long unsigned int totalMotionEvents' previously defined here
  109 | unsigned long totalMotionEvents = 0;
      |               ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:84:15: error: redefinition of 'long unsigned int totalPhotosSent'
   84 | unsigned long totalPhotosSent = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:110:15: note: 'long unsigned int totalPhotosSent' previously defined here
  110 | unsigned long totalPhotosSent = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:85:15: error: redefinition of 'long unsigned int totalCommandsProcessed'
   85 | unsigned long totalCommandsProcessed = 0;
      |               ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:111:15: note: 'long unsigned int totalCommandsProcessed' previously defined here
  111 | unsigned long totalCommandsProcessed = 0;
      |               ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:95:6: error: redefinition of 'bool initializeCamera()'
   95 | bool initializeCamera() {
      |      ^~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:121:6: note: 'bool initializeCamera()' previously defined here
  121 | bool initializeCamera() {
      |      ^~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:156:14: error: redefinition of 'camera_fb_t* currentFrameBuffer'
  156 | camera_fb_t* currentFrameBuffer = nullptr;
      |              ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:182:14: note: 'camera_fb_t* currentFrameBuffer' previously defined here
  182 | camera_fb_t* currentFrameBuffer = nullptr;
      |              ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:157:8: error: redefinition of 'size_t currentIndex'
  157 | size_t currentIndex = 0;
      |        ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:183:8: note: 'size_t currentIndex' previously defined here
  183 | size_t currentIndex = 0;
      |        ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:160:6: error: redefinition of 'bool isMoreDataAvailable()'
  160 | bool isMoreDataAvailable() {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:186:6: note: 'bool isMoreDataAvailable()' previously defined here
  186 | bool isMoreDataAvailable() {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:165:6: error: redefinition of 'byte getNextByte()'
  165 | byte getNextByte() {
      |      ^~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:191:6: note: 'byte getNextByte()' previously defined here
  191 | byte getNextByte() {
      |      ^~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:176:8: error: redefinition of 'String captureAndSendPhoto(String)'
  176 | String captureAndSendPhoto(String chatId) {
      |        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:202:8: note: 'String captureAndSendPhoto(String)' previously defined here
  202 | String captureAndSendPhoto(String chatId) {
      |        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:233:16: error: redefinition of 'void motionDetectionISR()'
  233 | void IRAM_ATTR motionDetectionISR() {
      |                ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:259:16: note: 'void motionDetectionISR()' previously defined here
  259 | void IRAM_ATTR motionDetectionISR() {
      |                ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:247:6: error: redefinition of 'bool initializeMotionDetection()'
  247 | bool initializeMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:273:6: note: 'bool initializeMotionDetection()' previously defined here
  273 | bool initializeMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:274:6: error: redefinition of 'void handleMotionDetection()'
  274 | void handleMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:300:6: note: 'void handleMotionDetection()' previously defined here
  300 | void handleMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:331:8: error: redefinition of 'String getSystemUptime()'
  331 | String getSystemUptime() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:357:8: note: 'String getSystemUptime()' previously defined here
  357 | String getSystemUptime() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:350:8: error: redefinition of 'String getWiFiInformation()'
  350 | String getWiFiInformation() {
      |        ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:376:8: note: 'String getWiFiInformation()' previously defined here
  376 | String getWiFiInformation() {
      |        ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:381:8: error: redefinition of 'String getSystemStatus()'
  381 | String getSystemStatus() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:407:8: note: 'String getSystemStatus()' previously defined here
  407 | String getSystemStatus() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:420:8: error: redefinition of 'String createCustomKeyboard()'
  420 | String createCustomKeyboard() {
      |        ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:446:8: note: 'String createCustomKeyboard()' previously defined here
  446 | String createCustomKeyboard() {
      |        ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:432:6: error: redefinition of 'void sendMessageWithKeyboard(String, String)'
  432 | void sendMessageWithKeyboard(String chatId, String message) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:458:6: note: 'void sendMessageWithKeyboard(String, String)' previously defined here
  458 | void sendMessageWithKeyboard(String chatId, String message) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:440:6: error: redefinition of 'void handleTelegramMessages()'
  440 | void handleTelegramMessages() {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:466:6: note: 'void handleTelegramMessages()' previously defined here
  466 | void handleTelegramMessages() {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:472:6: error: redefinition of 'void processCommand(String, String, String)'
  472 | void processCommand(String chatId, String command, String fromName) {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:498:6: note: 'void processCommand(String, String, String)' previously defined here
  498 | void processCommand(String chatId, String command, String fromName) {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino: In function 'void processCommand(String, String, String)':
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:484:5: error: 'handleFlashCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  484 |     handleFlashCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:487:5: error: 'handleStatusCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  487 |     handleStatusCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:490:5: error: 'handleRebootCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  490 |     handleRebootCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:493:5: error: 'handlePingCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  493 |     handlePingCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:496:5: error: 'handleMotionOnCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  496 |     handleMotionOnCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:499:5: error: 'handleMotionOffCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  499 |     handleMotionOffCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:502:5: error: 'handleUptimeCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  502 |     handleUptimeCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:505:5: error: 'handleWiFiCommand' was not declared in this scope; did you mean 'handleStartCommand'?
  505 |     handleWiFiCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handleStartCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:508:5: error: 'handleHelpCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  508 |     handleHelpCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:511:5: error: 'handleFaceOnCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  511 |     handleFaceOnCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:514:5: error: 'handleFaceOffCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  514 |     handleFaceOffCommand(chatId);
      |     ^~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:517:5: error: 'handleUnknownCommand' was not declared in this scope; did you mean 'handlePhotoCommand'?
  517 |     handleUnknownCommand(chatId, command);
      |     ^~~~~~~~~~~~~~~~~~~~
      |     handlePhotoCommand
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino: At global scope:
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:528:6: error: redefinition of 'void handleStartCommand(String, String)'
  528 | void handleStartCommand(String chatId, String fromName) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:554:6: note: 'void handleStartCommand(String, String)' previously defined here
  554 | void handleStartCommand(String chatId, String fromName) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:547:6: error: redefinition of 'void handlePhotoCommand(String)'
  547 | void handlePhotoCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:573:6: note: 'void handlePhotoCommand(String)' previously defined here
  573 | void handlePhotoCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:766:6: error: redefinition of 'void setup()'
  766 | void setup() {
      |      ^~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:687:6: note: 'void setup()' previously defined here
  687 | void setup() {
      |      ^~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:839:6: error: redefinition of 'void loop()'
  839 | void loop() {
      |      ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:812:6: note: 'void loop()' previously defined here
  812 | void loop() {
      |      ^~~~
exit status 1

Compilation error: conflicting declaration 'const char* TELEGRAM_CERTIFICATE_ROOT'