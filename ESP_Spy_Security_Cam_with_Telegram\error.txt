D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:21:13: error: redefinition of 'const char* WIFI_SSID'
   21 | const char* WIFI_SSID = "SKR";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:21:13: note: 'const char* WIFI_SSID' previously defined here
   21 | const char* WIFI_SSID = "SKR";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:22:13: error: redefinition of 'const char* WIFI_PASSWORD'
   22 | const char* WIFI_PASSWORD = "12345678";
      |             ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:22:13: note: 'const char* WIFI_PASSWORD' previously defined here
   22 | const char* WIFI_PASSWORD = "12345678";
      |             ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:23:13: error: redefinition of 'const char* BOT_TOKEN'
   23 | const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:23:13: note: 'const char* BOT_TOKEN' previously defined here
   23 | const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
      |             ^~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:24:13: error: redefinition of 'const char* CHAT_ID'
   24 | const char* CHAT_ID = "5283649744";
      |             ^~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:24:13: note: 'const char* CHAT_ID' previously defined here
   24 | const char* CHAT_ID = "5283649744";
      |             ^~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:61:18: error: redefinition of 'WiFiClientSecure secureClient'
   61 | WiFiClientSecure secureClient;
      |                  ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:61:18: note: 'WiFiClientSecure secureClient' previously declared here
   61 | WiFiClientSecure secureClient;
      |                  ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:62:22: error: redefinition of 'UniversalTelegramBot bot'
   62 | UniversalTelegramBot bot(BOT_TOKEN, secureClient);
      |                      ^~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:62:22: note: 'UniversalTelegramBot bot' previously declared here
   62 | UniversalTelegramBot bot(BOT_TOKEN, secureClient);
      |                      ^~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:65:15: error: redefinition of 'long unsigned int lastBotRequestTime'
   65 | unsigned long lastBotRequestTime = 0;
      |               ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:65:15: note: 'long unsigned int lastBotRequestTime' previously defined here
   65 | unsigned long lastBotRequestTime = 0;
      |               ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:66:15: error: redefinition of 'long unsigned int systemStartTime'
   66 | unsigned long systemStartTime = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:66:15: note: 'long unsigned int systemStartTime' previously defined here
   66 | unsigned long systemStartTime = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:67:15: error: redefinition of 'long unsigned int lastMotionTime'
   67 | unsigned long lastMotionTime = 0;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:67:15: note: 'long unsigned int lastMotionTime' previously defined here
   67 | unsigned long lastMotionTime = 0;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:68:24: error: redefinition of 'volatile long unsigned int lastMotionInterrupt'
   68 | volatile unsigned long lastMotionInterrupt = 0;
      |                        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:68:24: note: 'volatile long unsigned int lastMotionInterrupt' previously defined here
   68 | volatile unsigned long lastMotionInterrupt = 0;
      |                        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:71:6: error: redefinition of 'bool motionDetectionEnabled'
   71 | bool motionDetectionEnabled = true;
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:71:6: note: 'bool motionDetectionEnabled' previously defined here
   71 | bool motionDetectionEnabled = true;
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:72:6: error: redefinition of 'bool flashLedState'
   72 | bool flashLedState = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:72:6: note: 'bool flashLedState' previously defined here
   72 | bool flashLedState = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:73:6: error: redefinition of 'bool faceDetectionEnabled'
   73 | bool faceDetectionEnabled = false;
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:73:6: note: 'bool faceDetectionEnabled' previously defined here
   73 | bool faceDetectionEnabled = false;
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:74:6: error: redefinition of 'bool systemInitialized'
   74 | bool systemInitialized = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:74:6: note: 'bool systemInitialized' previously defined here
   74 | bool systemInitialized = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:75:6: error: redefinition of 'bool wifiConnected'
   75 | bool wifiConnected = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:75:6: note: 'bool wifiConnected' previously defined here
   75 | bool wifiConnected = false;
      |      ^~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:76:6: error: redefinition of 'bool telegramConnected'
   76 | bool telegramConnected = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:76:6: note: 'bool telegramConnected' previously defined here
   76 | bool telegramConnected = false;
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:79:15: error: redefinition of 'volatile bool motionDetected'
   79 | volatile bool motionDetected = false;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:79:15: note: 'volatile bool motionDetected' previously defined here
   79 | volatile bool motionDetected = false;
      |               ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:80:6: error: redefinition of 'bool motionInterruptAttached'
   80 | bool motionInterruptAttached = false;
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:80:6: note: 'bool motionInterruptAttached' previously defined here
   80 | bool motionInterruptAttached = false;
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:83:15: error: redefinition of 'long unsigned int totalMotionEvents'
   83 | unsigned long totalMotionEvents = 0;
      |               ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:83:15: note: 'long unsigned int totalMotionEvents' previously defined here
   83 | unsigned long totalMotionEvents = 0;
      |               ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:84:15: error: redefinition of 'long unsigned int totalPhotosSent'
   84 | unsigned long totalPhotosSent = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:84:15: note: 'long unsigned int totalPhotosSent' previously defined here
   84 | unsigned long totalPhotosSent = 0;
      |               ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:85:15: error: redefinition of 'long unsigned int totalCommandsProcessed'
   85 | unsigned long totalCommandsProcessed = 0;
      |               ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:85:15: note: 'long unsigned int totalCommandsProcessed' previously defined here
   85 | unsigned long totalCommandsProcessed = 0;
      |               ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:95:6: error: redefinition of 'bool initializeCamera()'
   95 | bool initializeCamera() {
      |      ^~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:95:6: note: 'bool initializeCamera()' previously defined here
   95 | bool initializeCamera() {
      |      ^~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:156:14: error: redefinition of 'camera_fb_t* currentFrameBuffer'
  156 | camera_fb_t* currentFrameBuffer = nullptr;
      |              ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:156:14: note: 'camera_fb_t* currentFrameBuffer' previously defined here
  156 | camera_fb_t* currentFrameBuffer = nullptr;
      |              ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:157:8: error: redefinition of 'size_t currentIndex'
  157 | size_t currentIndex = 0;
      |        ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:157:8: note: 'size_t currentIndex' previously defined here
  157 | size_t currentIndex = 0;
      |        ^~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:160:6: error: redefinition of 'bool isMoreDataAvailable()'
  160 | bool isMoreDataAvailable() {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:160:6: note: 'bool isMoreDataAvailable()' previously defined here
  160 | bool isMoreDataAvailable() {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:165:6: error: redefinition of 'byte getNextByte()'
  165 | byte getNextByte() {
      |      ^~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:165:6: note: 'byte getNextByte()' previously defined here
  165 | byte getNextByte() {
      |      ^~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:176:8: error: redefinition of 'String captureAndSendPhoto(String)'
  176 | String captureAndSendPhoto(String chatId) {
      |        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:176:8: note: 'String captureAndSendPhoto(String)' previously defined here
  176 | String captureAndSendPhoto(String chatId) {
      |        ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:233:16: error: redefinition of 'void motionDetectionISR()'
  233 | void IRAM_ATTR motionDetectionISR() {
      |                ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:233:16: note: 'void motionDetectionISR()' previously defined here
  233 | void IRAM_ATTR motionDetectionISR() {
      |                ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:247:6: error: redefinition of 'bool initializeMotionDetection()'
  247 | bool initializeMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:247:6: note: 'bool initializeMotionDetection()' previously defined here
  247 | bool initializeMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:274:6: error: redefinition of 'void handleMotionDetection()'
  274 | void handleMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:274:6: note: 'void handleMotionDetection()' previously defined here
  274 | void handleMotionDetection() {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:331:8: error: redefinition of 'String getSystemUptime()'
  331 | String getSystemUptime() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:331:8: note: 'String getSystemUptime()' previously defined here
  331 | String getSystemUptime() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:350:8: error: redefinition of 'String getWiFiInformation()'
  350 | String getWiFiInformation() {
      |        ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:350:8: note: 'String getWiFiInformation()' previously defined here
  350 | String getWiFiInformation() {
      |        ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:381:8: error: redefinition of 'String getSystemStatus()'
  381 | String getSystemStatus() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:381:8: note: 'String getSystemStatus()' previously defined here
  381 | String getSystemStatus() {
      |        ^~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:420:8: error: redefinition of 'String createCustomKeyboard()'
  420 | String createCustomKeyboard() {
      |        ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:420:8: note: 'String createCustomKeyboard()' previously defined here
  420 | String createCustomKeyboard() {
      |        ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:432:6: error: redefinition of 'void sendMessageWithKeyboard(String, String)'
  432 | void sendMessageWithKeyboard(String chatId, String message) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:432:6: note: 'void sendMessageWithKeyboard(String, String)' previously defined here
  432 | void sendMessageWithKeyboard(String chatId, String message) {
      |      ^~~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:440:6: error: redefinition of 'void handleTelegramMessages()'
  440 | void handleTelegramMessages() {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:440:6: note: 'void handleTelegramMessages()' previously defined here
  440 | void handleTelegramMessages() {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:472:6: error: redefinition of 'void processCommand(String, String, String)'
  472 | void processCommand(String chatId, String command, String fromName) {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:472:6: note: 'void processCommand(String, String, String)' previously defined here
  472 | void processCommand(String chatId, String command, String fromName) {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:528:6: error: redefinition of 'void handleStartCommand(String, String)'
  528 | void handleStartCommand(String chatId, String fromName) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:528:6: note: 'void handleStartCommand(String, String)' previously defined here
  528 | void handleStartCommand(String chatId, String fromName) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:547:6: error: redefinition of 'void handlePhotoCommand(String)'
  547 | void handlePhotoCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:547:6: note: 'void handlePhotoCommand(String)' previously defined here
  547 | void handlePhotoCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:556:6: error: redefinition of 'void handleFlashCommand(String)'
  556 | void handleFlashCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:556:6: note: 'void handleFlashCommand(String)' previously defined here
  556 | void handleFlashCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:568:6: error: redefinition of 'void handleStatusCommand(String)'
  568 | void handleStatusCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:568:6: note: 'void handleStatusCommand(String)' previously defined here
  568 | void handleStatusCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:576:6: error: redefinition of 'void handleRebootCommand(String)'
  576 | void handleRebootCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:576:6: note: 'void handleRebootCommand(String)' previously defined here
  576 | void handleRebootCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:585:6: error: redefinition of 'void handlePingCommand(String)'
  585 | void handlePingCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:585:6: note: 'void handlePingCommand(String)' previously defined here
  585 | void handlePingCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:592:6: error: redefinition of 'void handleMotionOnCommand(String)'
  592 | void handleMotionOnCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:592:6: note: 'void handleMotionOnCommand(String)' previously defined here
  592 | void handleMotionOnCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:600:6: error: redefinition of 'void handleMotionOffCommand(String)'
  600 | void handleMotionOffCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:600:6: note: 'void handleMotionOffCommand(String)' previously defined here
  600 | void handleMotionOffCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:608:6: error: redefinition of 'void handleUptimeCommand(String)'
  608 | void handleUptimeCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:608:6: note: 'void handleUptimeCommand(String)' previously defined here
  608 | void handleUptimeCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:622:6: error: redefinition of 'void handleWiFiCommand(String)'
  622 | void handleWiFiCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:622:6: note: 'void handleWiFiCommand(String)' previously defined here
  622 | void handleWiFiCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:630:6: error: redefinition of 'void handleHelpCommand(String)'
  630 | void handleHelpCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:630:6: note: 'void handleHelpCommand(String)' previously defined here
  630 | void handleHelpCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:653:6: error: redefinition of 'void handleFaceOnCommand(String)'
  653 | void handleFaceOnCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:653:6: note: 'void handleFaceOnCommand(String)' previously defined here
  653 | void handleFaceOnCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:661:6: error: redefinition of 'void handleFaceOffCommand(String)'
  661 | void handleFaceOffCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:661:6: note: 'void handleFaceOffCommand(String)' previously defined here
  661 | void handleFaceOffCommand(String chatId) {
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:669:6: error: redefinition of 'void handleUnknownCommand(String, String)'
  669 | void handleUnknownCommand(String chatId, String command) {
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:669:6: note: 'void handleUnknownCommand(String, String)' previously defined here
  669 | void handleUnknownCommand(String chatId, String command) {
      |      ^~~~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:688:6: error: redefinition of 'bool initializeWiFi()'
  688 | bool initializeWiFi() {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:688:6: note: 'bool initializeWiFi()' previously defined here
  688 | bool initializeWiFi() {
      |      ^~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:731:6: error: redefinition of 'bool initializeTelegram()'
  731 | bool initializeTelegram() {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:731:6: note: 'bool initializeTelegram()' previously defined here
  731 | bool initializeTelegram() {
      |      ^~~~~~~~~~~~~~~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:766:6: error: redefinition of 'void setup()'
  766 | void setup() {
      |      ^~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:766:6: note: 'void setup()' previously defined here
  766 | void setup() {
      |      ^~~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP32_CAM_Security_System_v2.ino:839:6: error: redefinition of 'void loop()'
  839 | void loop() {
      |      ^~~~
D:\IOT Projects\ESP32 Cam Motion Triggered Security camera with telegram\ESP_Spy_Security_Cam_with_Telegram\ESP_Spy_Security_Cam_with_Telegram.ino:839:6: note: 'void loop()' previously defined here
  839 | void loop() {
      |      ^~~~
exit status 1

Compilation error: redefinition of 'const char* WIFI_SSID'

