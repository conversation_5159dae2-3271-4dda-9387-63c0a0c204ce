# 🚀 ESP32-CAM Smart Security System with Telegram Bot Control

## 📋 Overview

A professional, feature-rich IoT security camera system built with ESP32-CAM that provides real-time motion detection, photo capture, and remote control via Telegram bot with custom keyboard interface.

## ✨ Key Features

### 🔧 Core Functionalities
- **📷 High-Quality Photo Capture** - UXGA resolution with PSRAM support
- **🚨 Motion Detection** - PIR sensor with interrupt-based detection
- **📱 Telegram Bot Control** - Custom keyboard interface with emojis
- **🌐 WiFi Connectivity** - Robust connection with auto-reconnect
- **🔒 Security** - Authorized user access only
- **⚡ Real-time Alerts** - Instant motion notifications with photos

### 🎛️ Telegram Commands
| Command | Function | Description |
|---------|----------|-------------|
| `/start` | 🏁 Welcome | Shows main menu with custom keyboard |
| `/photo` | 📸 Capture | Takes and sends a photo instantly |
| `/flash` | 💡 Flash Control | Toggles the onboard flash LED |
| `/status` | 📡 System Status | Comprehensive system information |
| `/reboot` | 🔁 Restart | Reboots the ESP32-CAM module |
| `/ping` | 🏓 Connection Test | Tests system responsiveness |
| `/motionon` | 🛰️ Enable Motion | Activates motion detection alerts |
| `/motionoff` | 🔕 Disable Motion | Deactivates motion detection |
| `/uptime` | ⏱ System Uptime | Shows total running time |
| `/wifi` | 🌐 WiFi Info | Network connection details |
| `/help` | 🆘 Help | Complete command reference |
| `/faceon` | 🧠 Face Detection | Enables face detection (future) |
| `/faceoff` | 🚫 Disable Face | Disables face detection |

## 🧰 Hardware Requirements

### 📦 Components List
- **ESP32-CAM AI Thinker Module** (with OV2640 camera)
- **PIR Motion Sensor** (HC-SR501 or similar)
- **FTDI USB to Serial Programmer** (for code upload)
- **Jumper Wires** (Male-to-Female)
- **Breadboard** (optional, for prototyping)
- **5V Power Supply** (for stable operation)

### 🔌 Pin Connections

```
ESP32-CAM AI Thinker Pinout:
┌─────────────────────────────┐
│  ESP32-CAM AI Thinker       │
│                             │
│  5V  ●  GND                 │
│  U0R ●  U0T                 │
│  GPIO16 ● GPIO0             │
│  GPIO14 ● GPIO2             │
│  GPIO15 ● GPIO4 (Flash LED) │
│  GPIO13 ● GPIO12            │
│  GND ●  VCC                 │
└─────────────────────────────┘

PIR Sensor Connections:
PIR VCC    → ESP32-CAM 5V
PIR GND    → ESP32-CAM GND  
PIR OUT    → ESP32-CAM GPIO13

FTDI Programmer (for upload only):
FTDI 5V    → ESP32-CAM 5V
FTDI GND   → ESP32-CAM GND
FTDI TX    → ESP32-CAM U0R
FTDI RX    → ESP32-CAM U0T
```

## 🛠️ Software Setup

### 📚 Required Libraries
Install these libraries in Arduino IDE:
```
1. ESP32 Board Package (by Espressif Systems)
2. UniversalTelegramBot (by Brian Lough)
3. ArduinoJson (by Benoit Blanchon)
```

### 🔧 Arduino IDE Configuration
1. **Board:** ESP32 Wrover Module
2. **Upload Speed:** 115200
3. **Flash Frequency:** 40MHz
4. **Flash Mode:** QIO
5. **Partition Scheme:** Huge APP (3MB No OTA/1MB SPIFFS)
6. **Core Debug Level:** None

### 🤖 Telegram Bot Setup

#### Step 1: Create Telegram Bot
1. Open Telegram and search for `@BotFather`
2. Send `/newbot` command
3. Choose a name for your bot (e.g., "ESP32 Security Cam")
4. Choose a username (e.g., "esp32_security_cam_bot")
5. Copy the **Bot Token** provided

#### Step 2: Get Your Chat ID
1. Send a message to your bot
2. Visit: `https://api.telegram.org/bot<YOUR_BOT_TOKEN>/getUpdates`
3. Find your **Chat ID** in the response

#### Step 3: Update Code Configuration
```cpp
const char* WIFI_SSID = "Your_WiFi_Name";
const char* WIFI_PASSWORD = "Your_WiFi_Password";
const char* BOT_TOKEN = "Your_Bot_Token_Here";
const char* CHAT_ID = "Your_Chat_ID_Here";
```

## 📤 Upload Process

### 🔄 Programming Mode
1. Connect FTDI programmer to ESP32-CAM
2. Connect GPIO0 to GND (programming mode)
3. Press RESET button on ESP32-CAM
4. Upload the code
5. Disconnect GPIO0 from GND
6. Press RESET button (normal operation)

### ⚡ Power Supply
- Use **5V external power supply** for stable operation
- USB power may be insufficient for camera and WiFi

## 🎯 Usage Instructions

### 🚀 First Boot
1. Power on the ESP32-CAM
2. Monitor Serial output (115200 baud)
3. Wait for WiFi connection and Telegram initialization
4. You'll receive a startup notification on Telegram

### 📱 Telegram Control
1. Open Telegram and find your bot
2. Send `/start` to see the custom keyboard
3. Use buttons for quick access to functions
4. Motion detection is enabled by default

### 🚨 Motion Alerts
- When motion is detected, you'll receive:
  1. Immediate alert message with timestamp
  2. Captured photo from the camera
  3. Event statistics

## 🔧 Troubleshooting

### ❌ Common Issues

**Boot Loop Problem:**
- Ensure proper power supply (5V, sufficient current)
- Check all connections
- Verify GPIO0 is not connected to GND during normal operation

**WiFi Connection Failed:**
- Verify SSID and password
- Check WiFi signal strength
- Ensure 2.4GHz network (ESP32 doesn't support 5GHz)

**Camera Initialization Failed:**
- Check camera module connection
- Verify PSRAM availability
- Try different power supply

**Telegram Not Working:**
- Verify bot token and chat ID
- Check internet connection
- Ensure correct time/date on router

### 🔍 Debug Tips
- Monitor Serial output at 115200 baud
- Check system status via `/status` command
- Use `/ping` to test connectivity
- Review memory usage in status report

## 📊 System Specifications

### 🔋 Performance
- **Boot Time:** ~10-15 seconds
- **Photo Capture:** 2-5 seconds
- **Motion Response:** <1 second
- **WiFi Reconnect:** 5-10 seconds
- **Memory Usage:** ~200KB RAM

### 📷 Camera Settings
- **Resolution:** UXGA (1600x1200) with PSRAM, SVGA (800x600) without
- **Format:** JPEG
- **Quality:** High (10) with PSRAM, Standard (12) without
- **Frame Rate:** On-demand capture

## 🛡️ Security Features

### 🔒 Access Control
- Single authorized Chat ID only
- Unauthorized access logging
- Secure HTTPS communication

### 🚨 Monitoring
- Motion event logging
- System health monitoring
- Automatic error recovery

## 📈 Future Enhancements

- **Face Detection** - AI-powered face recognition
- **Cloud Storage** - Photo backup to cloud services
- **Multiple Users** - Support for multiple authorized users
- **Scheduling** - Time-based motion detection
- **Mobile App** - Dedicated mobile application

## 📞 Support

For issues, questions, or contributions:
- Check the troubleshooting section
- Review Serial monitor output
- Test with `/status` and `/ping` commands

---

**🎉 Enjoy your professional ESP32-CAM Security System!** 🛡️
