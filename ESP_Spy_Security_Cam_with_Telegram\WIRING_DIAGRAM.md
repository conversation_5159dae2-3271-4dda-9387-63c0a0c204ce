# 🔌 ESP32-CAM Security System Wiring Diagram

## 📦 Components Required

- **ESP32-CAM AI Thinker Module** (with OV2640 camera)
- **PIR Motion Sensor** (HC-SR501 or similar)
- **FTDI USB to Serial Programmer** (for code upload)
- **Jumper Wires** (Male-to-Female)
- **5V Power Supply** (2A recommended)

## 🔌 Pin Connections

### ESP32-CAM AI Thinker Pinout
```
                ESP32-CAM AI Thinker
                ┌─────────────────────┐
                │                     │
                │  ●5V          GND●  │
                │  ●U0R         U0T●  │
                │  ●GPIO16    GPIO0●  │
                │  ●GPIO14    GPIO2●  │
                │  ●GPIO15    GPIO4●  │ ← Flash LED (built-in)
                │  ●GPIO13   GPIO12●  │ ← PIR Sensor Connection
                │  ●GND         VCC●  │
                │                     │
                │    [Camera Module]  │
                │         [Antenna]   │
                └─────────────────────┘
```

### PIR Motion Sensor Connections
```
PIR Sensor HC-SR501
┌─────────────────┐
│   ●VCC  ●OUT    │
│         ●GND    │
│                 │
│  [Sensor Dome]  │
│                 │
│ [Potentiometers]│
└─────────────────┘

Connections:
PIR VCC (Red)    → ESP32-CAM 5V
PIR GND (Black)  → ESP32-CAM GND  
PIR OUT (Yellow) → ESP32-CAM GPIO13
```

### FTDI Programmer (Upload Only)
```
FTDI USB-Serial Programmer
┌─────────────────┐
│ ●5V   ●3.3V     │
│ ●TX   ●RX       │
│ ●DTR  ●GND      │
│                 │
│    [USB Port]   │
└─────────────────┘

Programming Connections:
FTDI 5V  → ESP32-CAM 5V
FTDI GND → ESP32-CAM GND
FTDI TX  → ESP32-CAM U0R (RX)
FTDI RX  → ESP32-CAM U0T (TX)

For Programming Mode:
Connect GPIO0 to GND temporarily
```

## 🔧 Complete Wiring Diagram

```
                    5V Power Supply
                         │
                    ┌────┴────┐
                    │    +    │
                    │    -    │
                    └────┬────┘
                         │
        ┌────────────────┼────────────────┐
        │                │                │
        │           ESP32-CAM             │
        │        ┌─────────────────┐      │
        │        │                 │      │
    ┌───┴───┐    │  ●5V      GND●──┼──────┼─── GND (Black)
    │ PIR   │    │  ●U0R     U0T●  │      │
    │ VCC●──┼────┼──●GPIO16  GPIO0●│      │
    │ OUT●──┼────┼──●GPIO13  GPIO2●│      │
    │ GND●──┼────┼──●GND     GPIO4●│ ← Flash LED
    └───────┘    │           VCC●──┼──────┘
                 │                 │
                 │   [Camera]      │
                 └─────────────────┘

Legend:
● = Connection Point
─ = Wire Connection
┼ = Junction/Split
```

## ⚡ Power Requirements

### Power Supply Specifications
- **Voltage:** 5V DC
- **Current:** Minimum 2A (recommended)
- **Connector:** Barrel jack or direct wire connection

### Power Consumption
- **ESP32-CAM:** ~300mA (WiFi active)
- **PIR Sensor:** ~65mA (active)
- **Camera:** ~150mA (during capture)
- **Total:** ~500mA (normal operation)

⚠️ **Important:** USB power from FTDI may be insufficient for stable operation!

## 🔄 Programming Setup

### Step 1: Connect FTDI Programmer
```
FTDI → ESP32-CAM
5V   → 5V
GND  → GND
TX   → U0R
RX   → U0T
```

### Step 2: Enter Programming Mode
1. Connect **GPIO0 to GND** (programming mode)
2. Press **RESET** button on ESP32-CAM
3. Upload code via Arduino IDE
4. **Disconnect GPIO0 from GND**
5. Press **RESET** button (normal operation)

### Step 3: Normal Operation
- Remove FTDI programmer
- Connect 5V external power supply
- System should boot automatically

## 🛠️ Assembly Tips

### 📌 Connection Quality
- Use **solid connections** - loose wires cause issues
- **Solder connections** for permanent installation
- Use **breadboard** for prototyping only

### 🔍 Troubleshooting Connections
- **No boot:** Check power supply (5V, 2A)
- **Camera error:** Verify camera module connection
- **Motion not detected:** Check PIR sensor wiring
- **Upload failed:** Ensure GPIO0 connected to GND during programming

### 🔒 Mounting Considerations
- **PIR Sensor:** Mount 2-3 meters high, avoid direct sunlight
- **ESP32-CAM:** Ensure good WiFi signal reception
- **Power Supply:** Use weatherproof enclosure if outdoor

## 📊 Pin Usage Summary

| Pin | Function | Connection |
|-----|----------|------------|
| GPIO13 | PIR Input | PIR Sensor OUT |
| GPIO4 | Flash LED | Built-in LED |
| GPIO33 | Status LED | Built-in LED |
| 5V | Power | PIR VCC + Power Supply |
| GND | Ground | PIR GND + Power Supply |
| U0R/U0T | Serial | FTDI TX/RX (programming) |
| GPIO0 | Boot Mode | GND (programming only) |

## ⚠️ Safety Notes

- **Never exceed 5V** on power pins
- **Double-check polarity** before connecting power
- **Use proper gauge wire** for power connections
- **Ensure stable power supply** to prevent brownouts
- **Test connections** before final assembly

---

**🎯 Ready to build your professional security system!** 🛡️
