# 🛡️ ESP32-CAM Smart Security System - Complete Setup Guide

## 📋 Table of Contents
1. [🧰 Hardware Requirements](#-hardware-requirements)
2. [📚 Software Requirements](#-software-requirements)
3. [🔧 Hardware Assembly](#-hardware-assembly)
4. [🤖 Telegram Bot Setup](#-telegram-bot-setup)
5. [💻 Arduino IDE Configuration](#-arduino-ide-configuration)
6. [⚙️ Code Configuration](#️-code-configuration)
7. [📤 Uploading Code](#-uploading-code)
8. [✅ Testing & Verification](#-testing--verification)
9. [🚨 Troubleshooting](#-troubleshooting)
10. [🔧 Advanced Configuration](#-advanced-configuration)

---

## 🧰 Hardware Requirements

### 📦 Core Components
| Component | Quantity | Purpose | Notes |
|-----------|----------|---------|-------|
| 📷 ESP32-CAM AI Thinker | 1 | Main controller & camera | Includes built-in WiFi |
| 🔔 PIR Motion Sensor (HC-SR501) | 1 | Motion detection | 3-pin version recommended |
| 🔌 FTDI USB-TTL Programmer | 1 | Code upload | FT232RL or CP2102 based |
| 📡 Micro USB Cable | 1 | Programming connection | Data transfer capable |
| 🔌 5V 2A Power Adapter | 1 | Power supply | Stable power critical |
| 🔗 Jumper Wires | 6-8 pieces | Connections | Male-to-female recommended |

### 🔧 Optional Components
- 📦 Weatherproof enclosure (for outdoor use)
- 📐 Breadboard or PCB for permanent installation
- 🔌 Power bank for portable operation
- 📏 Extension wires for remote sensor placement

---

## 📚 Software Requirements

### 💻 Development Environment
- **Arduino IDE 2.0+** or **VS Code with PlatformIO**
- **ESP32 Board Package** (version 2.0.0+)
- **Required Libraries:**
  - `UniversalTelegramBot` (by Brian Lough)
  - `ArduinoJson` (version 6.x)
  - `WiFiClientSecure`
  - `esp_camera.h` (included in ESP32 package)

### 📱 Mobile Requirements
- **Telegram App** (iOS/Android/Desktop)
- Stable internet connection
- Telegram account

---

## 🔧 Hardware Assembly

### 🔌 Step 1: Power Connections
```
ESP32-CAM Power Pins:
┌─────────────────┐
│ 5V  ●           │ ← Connect to 5V power supply
│ GND ●           │ ← Connect to Ground
└─────────────────┘
```

### 🔔 Step 2: PIR Sensor Wiring
```
PIR Sensor (HC-SR501):
┌─────────────────┐
│ VCC ● ────────► │ ESP32-CAM 5V
│ OUT ● ────────► │ ESP32-CAM GPIO 13
│ GND ● ────────► │ ESP32-CAM GND
└─────────────────┘
```

### 📡 Step 3: FTDI Programming Setup
```
FTDI Programmer:
┌─────────────────┐
│ VCC ● ────────► │ ESP32-CAM 5V (optional)
│ GND ● ────────► │ ESP32-CAM GND
│ TXD ● ────────► │ ESP32-CAM GPIO 1 (RX)
│ RXD ● ────────► │ ESP32-CAM GPIO 0 (TX)
└─────────────────┘

Programming Mode: Connect GPIO 0 to GND during upload
```

### ⚠️ Assembly Notes
- **Power First:** Always connect power before signal wires
- **Double Check:** Verify all connections before powering on
- **GPIO 0:** Must be pulled to GND for programming mode
- **Stable Power:** Camera requires stable 5V supply

---

## 🤖 Telegram Bot Setup

### 📱 Step 1: Create Telegram Bot
1. Open Telegram and search for `@BotFather`
2. Start conversation: `/start`
3. Create new bot: `/newbot`
4. Choose bot name (e.g., "My Security Camera")
5. Choose username (e.g., "my_security_cam_bot")
6. **Save the Bot Token** (format: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`)

### 🆔 Step 2: Get Your Chat ID
1. Search for `@userinfobot` in Telegram
2. Start conversation: `/start`
3. **Save your Chat ID** (format: `123456789`)

### 🔐 Step 3: Test Bot Communication
1. Search for your bot username in Telegram
2. Start conversation: `/start`
3. Send test message to verify bot responds

---

## 💻 Arduino IDE Configuration

### 📦 Step 1: Install ESP32 Board Package
1. Open Arduino IDE
2. Go to `File` → `Preferences`
3. Add to "Additional Board Manager URLs":
   ```
   https://dl.espressif.com/dl/package_esp32_index.json
   ```
4. Go to `Tools` → `Board` → `Boards Manager`
5. Search "ESP32" and install "ESP32 by Espressif Systems"

### 📚 Step 2: Install Required Libraries
1. Go to `Tools` → `Manage Libraries`
2. Install these libraries:
   - **UniversalTelegramBot** by Brian Lough
   - **ArduinoJson** by Benoît Blanchon (version 6.x)

### ⚙️ Step 3: Board Configuration
Select these settings in Arduino IDE:
- **Board:** "AI Thinker ESP32-CAM"
- **CPU Frequency:** "240MHz (WiFi/BT)"
- **Flash Frequency:** "80MHz"
- **Flash Mode:** "QIO"
- **Partition Scheme:** "Huge APP (3MB No OTA/1MB SPIFFS)"
- **Core Debug Level:** "None"

---

## ⚙️ Code Configuration

### 🔧 Step 1: Update Configuration Section
Edit these lines in the main code:

```cpp
// 🔧 CONFIGURATION - MODIFY THESE VALUES
#define WIFI_SSID "YOUR_WIFI_SSID"        // Your WiFi network name
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD" // Your WiFi password
#define BOT_TOKEN "YOUR_TELEGRAM_BOT_TOKEN" // From BotFather
#define CHAT_ID "YOUR_TELEGRAM_CHAT_ID"     // Your Telegram Chat ID
```

### 📝 Configuration Example:
```cpp
#define WIFI_SSID "HomeNetwork"
#define WIFI_PASSWORD "mypassword123"
#define BOT_TOKEN "123456789:ABCdefGHIjklMNOpqrsTUVwxyz"
#define CHAT_ID "987654321"
```

### 🔍 Optional Configurations:
- **PIR_PIN:** Change motion sensor pin (default: GPIO 13)
- **FLASH_LED_PIN:** Flash LED pin (default: GPIO 4)
- **Motion Cooldown:** Adjust detection interval (default: 10 seconds)

---

## 📤 Uploading Code

### 🔌 Step 1: Programming Mode Setup
1. Connect FTDI programmer to ESP32-CAM
2. **Connect GPIO 0 to GND** (programming mode)
3. Connect USB cable to computer
4. Select correct COM port in Arduino IDE

### ⬆️ Step 2: Upload Process
1. Click "Upload" button in Arduino IDE
2. Wait for "Connecting..." message
3. **Press and release RESET button** on ESP32-CAM
4. Code should begin uploading
5. Wait for "Upload Complete" message

### 🔄 Step 3: Normal Operation Mode
1. **Disconnect GPIO 0 from GND**
2. Press RESET button
3. ESP32-CAM should boot normally
4. Check Serial Monitor for startup messages

### 📊 Step 4: Monitor Serial Output
Expected startup sequence:
```
🚀 ESP32-CAM Security System Starting...
🔔 Motion detection interrupt configured
✅ Camera initialized successfully!
📡 Connecting to WiFi...
✅ WiFi connected successfully!
🌍 IP Address: *************
✅ Telegram bot connected successfully!
🎉 Setup completed successfully!
```

---

## ✅ Testing & Verification

### 📱 Step 1: Telegram Communication Test
1. Open Telegram and find your bot
2. Send `/start` command
3. Verify custom keyboard appears
4. Test each command button

### 📸 Step 2: Camera Function Test
1. Send `/photo` command
2. Verify image is captured and sent
3. Check image quality and focus
4. Test `/flash` command

### 🔔 Step 3: Motion Detection Test
1. Ensure `/motionon` is enabled
2. Wave hand in front of PIR sensor
3. Verify motion alert message
4. Check automatic photo capture

### 📡 Step 4: System Status Test
1. Test `/status` command
2. Verify all system information
3. Check `/wifi` and `/uptime` commands
4. Test `/ping` for response time

---

## 🚨 Troubleshooting

### 🔌 Power & Connection Issues

#### ❌ ESP32-CAM won't power on
- **Check power supply:** Ensure 5V 2A adapter
- **Verify connections:** Double-check all wiring
- **Test with USB:** Try USB power for testing

#### 📡 WiFi connection fails
- **Check credentials:** Verify SSID and password
- **Signal strength:** Move closer to router
- **Network compatibility:** Ensure 2.4GHz network
- **Reset network:** Try different WiFi network

### 📷 Camera Issues

#### 📸 Camera initialization failed
- **Power supply:** Camera needs stable 5V
- **PSRAM:** Check if PSRAM is detected
- **Module type:** Verify AI Thinker ESP32-CAM
- **Connections:** Check camera module seating

#### 🖼️ Poor image quality
- **Lens focus:** Adjust camera lens focus
- **Lighting:** Ensure adequate lighting
- **PSRAM:** Enable PSRAM for better quality
- **Resolution:** Adjust frame size in code

### 🤖 Telegram Issues

#### 📱 Bot not responding
- **Token:** Verify bot token is correct
- **Chat ID:** Confirm chat ID matches
- **Network:** Check internet connection
- **Certificate:** Ensure HTTPS certificate valid

#### 🔔 Motion alerts not working
- **PIR sensor:** Check PIR wiring and power
- **GPIO pin:** Verify GPIO 13 connection
- **Interrupt:** Check interrupt configuration
- **Enable status:** Ensure motion detection enabled

### 🔧 Programming Issues

#### ⬆️ Upload fails
- **Programming mode:** Ensure GPIO 0 to GND
- **COM port:** Select correct serial port
- **Reset timing:** Press reset during "Connecting..."
- **FTDI driver:** Install proper FTDI drivers

#### 📊 Serial monitor blank
- **Baud rate:** Set to 115200 baud
- **COM port:** Verify correct port selected
- **Programming mode:** Disconnect GPIO 0 from GND
- **Reset device:** Press reset button

---

## 🔧 Advanced Configuration

### 📷 Camera Quality Settings
```cpp
// High Quality Mode (requires PSRAM)
config.frame_size = FRAMESIZE_UXGA;     // 1600x1200
config.jpeg_quality = 10;               // Lower = better quality
config.fb_count = 2;                    // Frame buffers

// Standard Mode (no PSRAM)
config.frame_size = FRAMESIZE_SVGA;     // 800x600
config.jpeg_quality = 12;
config.fb_count = 1;
```

### 🔔 Motion Detection Tuning
```cpp
// Adjust motion detection sensitivity
const unsigned long motionCooldown = 10000;  // 10 seconds between alerts
const unsigned long motionDelay = 500;       // Stabilization delay
```

### 📡 Network Optimization
```cpp
// Telegram request timing
unsigned long botRequestDelay = 1000;        // 1 second between checks
unsigned long connectionTimeout = 30000;     // 30 second timeout
```

### 🔒 Security Enhancements
```cpp
// Multiple authorized users
const String AUTHORIZED_USERS[] = {
  "123456789",    // Primary user
  "987654321",    // Secondary user
};

// Command logging
void logCommand(String user, String command) {
  Serial.println("User: " + user + " Command: " + command);
}
```

### 🌐 Web Server Integration
```cpp
// Optional: Add web server for local access
#include <WebServer.h>
WebServer server(80);

void handleRoot() {
  server.send(200, "text/html", "ESP32-CAM Security System");
}
```

---

## 📋 Command Reference

| Command | Function | Description |
|---------|----------|-------------|
| 🏁 `/start` | Welcome | Shows system status and custom keyboard |
| 📸 `/photo` | Capture | Takes and sends a photo immediately |
| 💡 `/flash` | Flash Toggle | Turns flash LED on/off |
| 📡 `/status` | System Info | Shows detailed system status |
| ⏱ `/uptime` | Runtime | Shows system uptime |
| 🛰️ `/motionon` | Enable Motion | Enables automatic motion alerts |
| 🔕 `/motionoff` | Disable Motion | Disables motion detection |
| 🌐 `/wifi` | Network Info | Shows WiFi connection details |
| 🏓 `/ping` | Connectivity | Tests bot response |
| 🔁 `/reboot` | Restart | Reboots the ESP32-CAM |
| 🆘 `/help` | Help Menu | Lists all available commands |
| 🧠 `/faceon` | Face Detection | Enables face detection (if available) |
| 🚫 `/faceoff` | Disable Face | Disables face detection |

---

## 📞 Support & Resources

### 🔗 Useful Links
- **ESP32-CAM Documentation:** [Espressif Docs](https://docs.espressif.com)
- **Telegram Bot API:** [Core Bot API](https://core.telegram.org/bots/api)
- **Arduino Libraries:** [Library Manager](https://www.arduino.cc/reference/en/libraries/)

### 🤝 Community Support
- **ESP32 Forum:** [ESP32.com Community](https://esp32.com)
- **Arduino Forum:** [Arduino Community](https://forum.arduino.cc)
- **GitHub Issues:** Project repository for bug reports

### 📝 Changelog & Updates
- **v1.0:** Initial release with basic functionality
- **v1.1:** Added face detection support
- **v1.2:** Improved error handling and stability
- **v1.3:** Enhanced Telegram UI and commands

---

## 🎉 Congratulations!

Your ESP32-CAM Smart Security System is now ready! 🛡️

The system provides:
- ✅ **Real-time motion detection**
- 📸 **Automatic photo capture**
- 📱 **Telegram remote control**
- 🔔 **Instant security alerts**
- 💡 **Professional interface**

**Enjoy your new smart security system!** 🚀