#include "esp_camera.h"
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <UniversalTelegramBot.h>
#include <ArduinoJson.h>

// ===========================================
// 🔧 CONFIGURATION - MODIFY THESE VALUES
// ===========================================
#define WIFI_SSID "SKR"
#define WIFI_PASSWORD "12345678"
#define BOT_TOKEN "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U"
#define CHAT_ID "5283649744"

// 📌 Hardware Pin Definitions
#define PIR_PIN 13
#define FLASH_LED_PIN 4
#define CAMERA_MODEL_AI_THINKER

// ===========================================
// 📷 CAMERA CONFIGURATION
// ===========================================
#if defined(CAMERA_MODEL_AI_THINKER)
  #define PWDN_GPIO_NUM     32
  #define RESET_GPIO_NUM    -1
  #define XCLK_GPIO_NUM      0
  #define SIOD_GPIO_NUM     26
  #define SIOC_GPIO_NUM     27
  #define Y9_GPIO_NUM       35
  #define Y8_GPIO_NUM       34
  #define Y7_GPIO_NUM       39
  #define Y6_GPIO_NUM       36
  #define Y5_GPIO_NUM       21
  #define Y4_GPIO_NUM       19
  #define Y3_GPIO_NUM       18
  #define Y2_GPIO_NUM        5
  #define VSYNC_GPIO_NUM    25
  #define HREF_GPIO_NUM     23
  #define PCLK_GPIO_NUM     22
#endif

// ===========================================
// 🌐 GLOBAL VARIABLES
// ===========================================
WiFiClientSecure clientTCP;
UniversalTelegramBot bot(BOT_TOKEN, clientTCP);

// 🕒 Timing Variables
unsigned long lastTimeBotRan = 0;
unsigned long botRequestDelay = 1000;
unsigned long startTime = 0;

// 🔔 System State Variables
bool motionDetectionEnabled = true;
bool flashState = false;
bool faceDetectionEnabled = false;
volatile bool motionDetected = false;
unsigned long lastMotionTime = 0;
const unsigned long motionCooldown = 10000; // 10 seconds cooldown

// ===========================================
// 📷 CAMERA FUNCTIONS
// ===========================================
bool initCamera() {
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sscb_sda = SIOD_GPIO_NUM;
  config.pin_sscb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;
  config.pixel_format = PIXFORMAT_JPEG;
  
  // Check PSRAM availability for better quality
  if(psramFound()){
    config.frame_size = FRAMESIZE_UXGA;
    config.jpeg_quality = 10;
    config.fb_count = 2;
    Serial.println("📦 PSRAM found - High quality mode enabled");
  } else {
    config.frame_size = FRAMESIZE_SVGA;
    config.jpeg_quality = 12;
    config.fb_count = 1;
    Serial.println("⚠️ PSRAM not found - Standard quality mode");
  }

  esp_err_t err = esp_camera_init(&config);
  if (err != ESP_OK) {
    Serial.printf("❌ Camera init failed with error 0x%x\n", err);
    return false;
  }
  
  Serial.println("✅ Camera initialized successfully!");
  return true;
}

// Helper functions for photo sending
camera_fb_t * currentFrameBuffer = nullptr;
size_t currentIndex = 0;

bool isMoreDataAvailable() {
  return currentFrameBuffer && (currentIndex < currentFrameBuffer->len);
}

byte getNextByte() {
  if (currentFrameBuffer && currentIndex < currentFrameBuffer->len) {
    return currentFrameBuffer->buf[currentIndex++];
  }
  return 0;
}

String capturePhotoAndSend(String chat_id) {
  // Feed watchdog timer
  yield();
  
  currentFrameBuffer = esp_camera_fb_get();
  currentIndex = 0;
  
  if(!currentFrameBuffer) {
    Serial.println("❌ Camera capture failed");
    return "❌ *Camera Error*\nFailed to capture photo. Try again!";
  }
  
  Serial.printf("📷 Photo captured: %d bytes\n", currentFrameBuffer->len);
  
  // Feed watchdog before sending
  yield();
  
  bool success = bot.sendPhotoByBinary(chat_id, "image/jpeg", currentFrameBuffer->len, 
                                      isMoreDataAvailable, getNextByte, 
                                      nullptr, nullptr);
  
  esp_camera_fb_return(currentFrameBuffer);
  currentFrameBuffer = nullptr;
  
  if(success) {
    Serial.println("✅ Photo sent successfully!");
    return "📸 *Photo Sent Successfully!*\nImage captured and delivered! 🎯";
  } else {
    Serial.println("❌ Failed to send photo");
    return "❌ *Send Failed*\nPhoto captured but failed to send. Check connection! 📡";
  }
}

// ===========================================
// 🔔 MOTION DETECTION
// ===========================================
void IRAM_ATTR motionISR() {
  motionDetected = true;
}

void handleMotionDetection() {
  // Check motion via interrupt or polling
  bool currentMotion = motionDetected || digitalRead(PIR_PIN);
  
  if (currentMotion && motionDetectionEnabled) {
    unsigned long currentTime = millis();
    
    // Cooldown period to prevent spam
    if (currentTime - lastMotionTime > motionCooldown) {
      lastMotionTime = currentTime;
      
      Serial.println("🚨 Motion detected! Capturing photo...");
      
      // Flash LED briefly for indication (non-blocking)
      digitalWrite(FLASH_LED_PIN, HIGH);
      
      // Send alert message first
      String alertMsg = "🚨 **MOTION ALERT** 🚨\n\n";
      alertMsg += "Motion detected at: `" + getFormattedTime() + "`\n";
      alertMsg += "📍 Location: *ESP32-CAM Security System*\n";
      alertMsg += "📸 Capturing photo now...\n\n";
      alertMsg += "_Automatic security monitoring active_ 🛡️";
      
      bot.sendMessage(CHAT_ID, alertMsg, "Markdown");
      
      // Turn off flash after message sent
      digitalWrite(FLASH_LED_PIN, LOW);
      
      // Capture and send photo (with watchdog feed)
      yield(); // Feed watchdog
      String photoResult = capturePhotoAndSend(CHAT_ID);
      bot.sendMessage(CHAT_ID, photoResult, "Markdown");
    }
    
    motionDetected = false;
  }
}

// ===========================================
// ⏰ UTILITY FUNCTIONS
// ===========================================
String getFormattedTime() {
  unsigned long uptime = millis() / 1000;
  unsigned long days = uptime / 86400;
  unsigned long hours = (uptime % 86400) / 3600;
  unsigned long minutes = (uptime % 3600) / 60;
  unsigned long seconds = uptime % 60;
  
  String timeStr = "";
  if (days > 0) timeStr += String(days) + "d ";
  if (hours > 0) timeStr += String(hours) + "h ";
  if (minutes > 0) timeStr += String(minutes) + "m ";
  timeStr += String(seconds) + "s";
  
  return timeStr;
}

String getWiFiInfo() {
  String info = "🌐 **WiFi Information**\n\n";
  info += "📶 **SSID:** `" + WiFi.SSID() + "`\n";
  info += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
  info += "📡 **Signal Strength:** `" + String(WiFi.RSSI()) + " dBm`\n";
  info += "🔗 **MAC Address:** `" + WiFi.macAddress() + "`\n";
  
  // Signal quality description
  int rssi = WiFi.RSSI();
  String quality;
  if (rssi > -30) quality = "Excellent 🟢";
  else if (rssi > -50) quality = "Very Good 🟡";
  else if (rssi > -70) quality = "Good 🟠";
  else if (rssi > -80) quality = "Fair 🔴";
  else quality = "Poor ⚫";
  
  info += "📊 **Quality:** " + quality;
  
  return info;
}

// ===========================================
// 💬 TELEGRAM COMMAND HANDLERS
// ===========================================
void handleTelegramMessages() {
  int numNewMessages = bot.getUpdates(bot.last_message_received + 1);
  
  while (numNewMessages) {
    Serial.println("📨 New message received");
    
    for (int i = 0; i < numNewMessages; i++) {
      String chat_id = String(bot.messages[i].chat_id);
      
      // Security check - only respond to authorized chat_id
      if (chat_id != CHAT_ID) {
        Serial.println("🚫 Unauthorized access attempt from: " + chat_id);
        bot.sendMessage(chat_id, "🚫 *Access Denied*\nUnauthorized user!", "Markdown");
        continue;
      }
      
      String text = bot.messages[i].text;
      String from_name = bot.messages[i].from_name;
      
      Serial.println("👤 From: " + from_name + " | 💭 Message: " + text);
      
      // Command Processing
      if (text == "/start") {
        String welcome = "🏁 **ESP32-CAM Security System**\n\n";
        welcome += "Welcome *" + from_name + "*! 👋\n\n";
        welcome += "🛡️ Your smart security system is **ONLINE** and ready!\n\n";
        welcome += "📋 **Available Commands:**\n";
        welcome += "📸 /photo - Capture photo\n";
        welcome += "💡 /flash - Toggle flash\n";
        welcome += "📡 /status - System status\n";
        welcome += "🛰️ /motionon - Enable motion alerts\n";
        welcome += "🔕 /motionoff - Disable motion alerts\n";
        welcome += "🌐 /wifi - WiFi information\n";
        welcome += "🏓 /ping - Test connection\n";
        welcome += "⏱ /uptime - System uptime\n";
        welcome += "🔁 /reboot - Restart system\n";
        welcome += "🆘 /help - Show help\n\n";
        welcome += "🔒 *System Status:* ✅ Active\n";
        welcome += "📡 *Connection:* ✅ Stable\n";
        welcome += "🛰️ *Motion Detection:* " + String(motionDetectionEnabled ? "✅ Enabled" : "❌ Disabled");
        
        bot.sendMessage(chat_id, welcome, "Markdown");
      }
      
      else if (text == "/photo") {
        bot.sendMessage(chat_id, "📸 *Capturing Photo...*\nPlease wait while I take a picture! 📷", "Markdown");
        String result = capturePhotoAndSend(chat_id);
        bot.sendMessage(chat_id, result, "Markdown");
      }
      
      else if (text == "/flash") {
        flashState = !flashState;
        digitalWrite(FLASH_LED_PIN, flashState ? HIGH : LOW);
        String msg = flashState ? "💡 *Flash LED ON*\nFlash light activated! ✨" : "🌙 *Flash LED OFF*\nFlash light deactivated! 💤";
        bot.sendMessage(chat_id, msg, "Markdown");
      }
      
      else if (text == "/status") {
        String status = "📡 **System Status Report**\n\n";
        status += "🟢 **System:** Online & Operational\n";
        status += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
        status += "📶 **WiFi RSSI:** `" + String(WiFi.RSSI()) + " dBm`\n";
        status += "⏱ **Uptime:** `" + getFormattedTime() + "`\n";
        status += "🛰️ **Motion Detection:** " + String(motionDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
        status += "💡 **Flash LED:** " + String(flashState ? "✅ ON" : "❌ OFF") + "\n";
        status += "🧠 **Face Detection:** " + String(faceDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
        status += "💾 **Free Heap:** `" + String(ESP.getFreeHeap()) + " bytes`\n";
        status += "🔋 **PSRAM:** " + String(psramFound() ? "✅ Available" : "❌ Not Found");
        
        bot.sendMessage(chat_id, status, "Markdown");
      }
      
      else if (text == "/reboot") {
        bot.sendMessage(chat_id, "🔁 *Rebooting System...*\nESP32-CAM will restart now! ⚡\n\n_Please wait 30 seconds before reconnecting_ ⏳", "Markdown");
        delay(1000);
        ESP.restart();
      }
      
      else if (text == "/ping") {
        bot.sendMessage(chat_id, "🏓 **PONG!** 🎾\n\n⚡ *Response Time:* Quick!\n✅ *Status:* System Responsive\n📶 *Connection:* Stable", "Markdown");
      }
      
      else if (text == "/motionon") {
        motionDetectionEnabled = true;
        bot.sendMessage(chat_id, "🛰️ **Motion Detection ENABLED**\n\n✅ System will now monitor for motion\n🚨 Alerts will be sent automatically\n📸 Photos will be captured on detection\n\n_Stay secure!_ 🛡️", "Markdown");
      }
      
      else if (text == "/motionoff") {
        motionDetectionEnabled = false;
        bot.sendMessage(chat_id, "🔕 **Motion Detection DISABLED**\n\n❌ Motion monitoring stopped\n😴 System in quiet mode\n📵 No automatic alerts\n\n_Manual control only_ 🎛️", "Markdown");
      }
      
      else if (text == "/uptime") {
        String uptime = "⏱ **System Uptime**\n\n";
        uptime += "🕐 **Running for:** `" + getFormattedTime() + "`\n";
        uptime += "🔄 **Started:** " + String((millis() - (millis() % 1000)) / 1000) + " seconds ago\n";
        uptime += "⚡ **Status:** Stable & Operational\n\n";
        uptime += "_System has been running smoothly!_ ✨";
        
        bot.sendMessage(chat_id, uptime, "Markdown");
      }
      
      else if (text == "/wifi") {
        bot.sendMessage(chat_id, getWiFiInfo(), "Markdown");
      }
      
      else if (text == "/help") {
        String help = "🆘 **Command Reference Guide**\n\n";
        help += "📋 **Available Commands:**\n\n";
        help += "🏁 `/start` - *Welcome & status overview*\n";
        help += "📸 `/photo` - *Capture & send photo*\n";
        help += "💡 `/flash` - *Toggle flash LED*\n";
        help += "📡 `/status` - *Detailed system status*\n";
        help += "🔁 `/reboot` - *Restart the system*\n";
        help += "🏓 `/ping` - *Test connection*\n";
        help += "🛰️ `/motionon` - *Enable motion alerts*\n";
        help += "🔕 `/motionoff` - *Disable motion alerts*\n";
        help += "⏱ `/uptime` - *Show system uptime*\n";
        help += "🌐 `/wifi` - *WiFi connection info*\n";
        help += "🧠 `/faceon` - *Enable face detection*\n";
        help += "🚫 `/faceoff` - *Disable face detection*\n\n";
        help += "💡 **Quick Tip:** Commands are case-sensitive! ⚡";
        
        bot.sendMessage(chat_id, help, "Markdown");
      }
      
      else if (text == "/faceon") {
        faceDetectionEnabled = true;
        bot.sendMessage(chat_id, "🧠 **Face Detection ENABLED**\n\n✅ AI face recognition active\n👤 Will detect human faces\n🎯 Enhanced security mode\n\n_Smart monitoring activated!_ 🤖", "Markdown");
      }
      
      else if (text == "/faceoff") {
        faceDetectionEnabled = false;
        bot.sendMessage(chat_id, "🚫 **Face Detection DISABLED**\n\n❌ Face recognition stopped\n📷 Basic camera mode only\n⚡ Reduced processing load\n\n_Standard monitoring mode_ 📸", "Markdown");
      }
      
      else {
        String unknown = "❓ **Unknown Command**\n\n";
        unknown += "Command `" + text + "` not recognized.\n\n";
        unknown += "💡 **Try these instead:**\n";
        unknown += "• Type `/help` for all commands\n";
        unknown += "• Type `/start` to begin\n";
        unknown += "• Type `/status` for system info\n\n";
        unknown += "_I'm here to help!_ 🤖";
        
        bot.sendMessage(chat_id, unknown, "Markdown");
      }
    }
    
    numNewMessages = bot.getUpdates(bot.last_message_received + 1);
  }
}

// ===========================================
// 🚀 SETUP FUNCTION
// ===========================================
void setup() {
  Serial.begin(115200);
  Serial.println();
  Serial.println("🚀 ESP32-CAM Security System Starting...");
  
  // Record start time
  startTime = millis();
  
  // Initialize pins
  pinMode(PIR_PIN, INPUT);
  pinMode(FLASH_LED_PIN, OUTPUT);
  digitalWrite(FLASH_LED_PIN, LOW);
  
  // Setup motion detection interrupt (with error handling)
  if (digitalPinToInterrupt(PIR_PIN) != -1) {
    attachInterrupt(digitalPinToInterrupt(PIR_PIN), motionISR, RISING);
    Serial.println("🔔 Motion detection interrupt configured");
  } else {
    Serial.println("⚠️ PIR pin doesn't support interrupts, using polling method");
  }
  
  // Initialize camera
  if (!initCamera()) {
    Serial.println("❌ Camera initialization failed!");
    while(1);
  }
  
  // Connect to WiFi
  Serial.println("📡 Connecting to WiFi...");
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  int attempts = 0;
  while (WiFi.status() != WL_CONNECTED && attempts < 20) {
    delay(1000);
    Serial.print(".");
    attempts++;
  }
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi connected successfully!");
    Serial.print("🌍 IP Address: ");
    Serial.println(WiFi.localIP());
    Serial.print("📶 Signal Strength: ");
    Serial.print(WiFi.RSSI());
    Serial.println(" dBm");
  } else {
    Serial.println("\n❌ WiFi connection failed!");
    while(1);
  }
  
  // Configure Telegram secure client
  clientTCP.setInsecure(); // Use this for newer ESP32 core versions
  
  // Test Telegram connection
  Serial.println("🤖 Testing Telegram connection...");
  bool botConnected = bot.getMe(); // Changed this line - now it returns bool
  if (botConnected) {
    Serial.println("✅ Telegram bot connected successfully!");
    
    // Send startup notification
    String startupMsg = "🚀 **System Online!**\n\n";
    startupMsg += "✅ ESP32-CAM Security System started\n";
    startupMsg += "🌍 IP: `" + WiFi.localIP().toString() + "`\n";
    startupMsg += "📶 WiFi: `" + String(WiFi.RSSI()) + " dBm`\n";
    startupMsg += "🛰️ Motion Detection: ✅ Active\n\n";
    startupMsg += "_Your security system is ready!_ 🛡️";
    
    bot.sendMessage(CHAT_ID, startupMsg, "Markdown");
  } else {
    Serial.println("❌ Telegram connection failed!");
    // You can choose to continue or halt here depending on your preference
  }
  
  Serial.println("🎉 Setup completed successfully!");
  Serial.println("🔒 Security system is now ACTIVE and monitoring...");
}

// ===========================================
// 🔄 MAIN LOOP
// ===========================================
void loop() {
  // Feed watchdog timer to prevent resets
  yield();
  
  // Handle motion detection
  handleMotionDetection();
  
  // Handle Telegram messages (with timing control)
  if (millis() > lastTimeBotRan + botRequestDelay) {
    handleTelegramMessages();
    lastTimeBotRan = millis();
  }
  
  // Watchdog feed and small delay
  delay(50); // Increased delay to reduce load
}