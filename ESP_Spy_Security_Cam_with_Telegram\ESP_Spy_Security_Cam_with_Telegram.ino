/*
 * 🚀 ESP32-CAM Smart Security System with Telegram Bot Control
 *
 * Professional IoT Security Camera with Motion Detection
 * Features: WiFi connectivity, Telegram bot control, Motion alerts, Custom UI
 *
 * Hardware: ESP32-CAM AI Thinker + PIR Sensor + Flash LED
 * Author: IoT Security Expert
 * Version: 2.0 Professional Edition
 */

#include "esp_camera.h"
#include <WiFi.h>
#include <WiFiClientSecure.h>
#include <UniversalTelegramBot.h>
#include <ArduinoJson.h>

// ===========================================
// 🔧 CONFIGURATION - MODIFY THESE VALUES
// ===========================================
const char* WIFI_SSID = "SKR";
const char* WIFI_PASSWORD = "12345678";
const char* BOT_TOKEN = "7713931953:AAGV6R4E3-hJRiwuMznALM7_DvMNDSBkp3U";
const char* CHAT_ID = "5283649744";

// 📌 Hardware Pin Definitions
#define PIR_PIN 13
#define FLASH_LED_PIN 4
#define STATUS_LED_PIN 33  // Built-in LED for status indication

// 🔧 System Configuration
#define WIFI_TIMEOUT_MS 30000      // 30 seconds WiFi timeout
#define MOTION_DEBOUNCE_MS 1000    // 1 second motion debounce
#define MOTION_COOLDOWN_MS 15000   // 15 seconds between motion alerts
#define BOT_REQUEST_DELAY 1000     // 1 second between bot requests
#define CAMERA_RETRY_COUNT 3       // Camera initialization retries

// 📷 ESP32-CAM AI Thinker Pin Configuration
#define PWDN_GPIO_NUM     32
#define RESET_GPIO_NUM    -1
#define XCLK_GPIO_NUM      0
#define SIOD_GPIO_NUM     26
#define SIOC_GPIO_NUM     27
#define Y9_GPIO_NUM       35
#define Y8_GPIO_NUM       34
#define Y7_GPIO_NUM       39
#define Y6_GPIO_NUM       36
#define Y5_GPIO_NUM       21
#define Y4_GPIO_NUM       19
#define Y3_GPIO_NUM       18
#define Y2_GPIO_NUM        5
#define VSYNC_GPIO_NUM    25
#define HREF_GPIO_NUM     23
#define PCLK_GPIO_NUM     22

// 🔐 Telegram Root Certificate for HTTPS
const char* TELEGRAM_CERTIFICATE_ROOT = \
"-----BEGIN CERTIFICATE-----\n" \
"MIIDxTCCAq2gAwIBAgIQAqxcJmoLQJuPC3nyrkYldzANBgkqhkiG9w0BAQUFADBs\n" \
"MQswCQYDVQQGEwJVUzEVMBMGA1UEChMMRGlnaUNlcnQgSW5jMRkwFwYDVQQLExB3\n" \
"d3cuZGlnaWNlcnQuY29tMSswKQYDVQQDEyJEaWdpQ2VydCBIaWdoIEFzc3VyYW5j\n" \
"ZSBFViBSb290IENBMB4XDTA2MTExMDAwMDAwMFoXDTMxMTExMDAwMDAwMFowbDEL\n" \
"MAkGA1UEBhMCVVMxFTATBgNVBAoTDERpZ2lDZXJ0IEluYzEZMBcGA1UECxMQd3d3\n" \
"LmRpZ2ljZXJ0LmNvbTErMCkGA1UEAxMiRGlnaUNlcnQgSGlnaCBBc3N1cmFuY2Ug\n" \
"RVYgUm9vdCBDQTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBAMbM5XPm\n" \
"+9S75S0tMqbf5YE/yc0lSbZxKsPVlDRnogocsF9ppkCxxLeyj9CYpKlBWTrT3JTW\n" \
"PNt0OKRKzE0lgvdKpVMSOO7zSW1xkX5jtqumX8OkhPhPYlG++MXs2ziS4wblCJEM\n" \
"xChBVfvLWokVfnHoNb9Ncgk9vjo4UFt3MRuNs8ckRZqnrG0AFFoEt7oT61EKmEFB\n" \
"Ik5lYYeBQVCmeVyJ3hlKV9Uu5l0cUyx+mM0aBhakaHPQNAQTXKFx01p8VdteZOE3\n" \
"hzBWBOURtCmAEvF5OYiiAhF8J2a3iLd48soKqDirCmTCv2ZdlYTBoSUeh10aUAsg\n" \
"EsxBu24LUTi4S8sCAwEAAaNjMGEwDgYDVR0PAQH/BAQDAgGGMA8GA1UdEwEB/wQF\n" \
"MAMBAf8wHQYDVR0OBBYEFLE+w2kD+L9HAdSYJhoIAu9jZCvDMB8GA1UdIwQYMBaA\n" \
"FLE+w2kD+L9HAdSYJhoIAu9jZCvDMA0GCSqGSIb3DQEBBQUAA4IBAQAcGgaX3Nec\n" \
"nzyIZgYIVyHbIUf4KmeqvxgydkAQV8GK83rZEWWONfqe/EW1ntlMMUu4kehDLI6z\n" \
"eM7b41N5cdblIZQB2lWHmiRk9opmzN6cN82oNLFpmyPInngiK3BD41VHMWEZ71jF\n" \
"hS9OMPagMRYjyOfiZRYzy78aG6A9+MpeizGLYAiJLQwGXFK3xPkKmNEVX58Svnw2\n" \
"Yzi9RKR/5CYrCsSXaQ3pjOLAEFe4yHYSkVXySGnYvCoCWw9E1CAx2/S6cCZdkGCe\n" \
"vEsXCS+0yx5DaMkHJ8HSXPfqIbloEpw8nL+e/IBcm2PN7EeqJSdnoDfzAIJ9VNep\n" \
"+OkuE6N36B9K\n" \
"-----END CERTIFICATE-----\n";

// ===========================================
// 🌐 GLOBAL VARIABLES & SYSTEM STATE
// ===========================================

// 📡 Network & Communication Objects
WiFiClientSecure secureClient;
UniversalTelegramBot bot(BOT_TOKEN, secureClient);

// ⏰ Timing Control Variables
unsigned long lastBotRequestTime = 0;
unsigned long systemStartTime = 0;
unsigned long lastMotionTime = 0;
volatile unsigned long lastMotionInterrupt = 0;

// 🔔 System State Flags
bool motionDetectionEnabled = true;
bool flashLedState = false;
bool faceDetectionEnabled = false;
bool systemInitialized = false;
bool wifiConnected = false;
bool telegramConnected = false;

// 🚨 Motion Detection Variables
volatile bool motionDetected = false;
bool motionInterruptAttached = false;

// 📊 System Statistics
unsigned long totalMotionEvents = 0;
unsigned long totalPhotosSent = 0;
unsigned long totalCommandsProcessed = 0;

// ===========================================
// 📷 CAMERA INITIALIZATION & CONTROL
// ===========================================

/**
 * Initialize ESP32-CAM with optimal settings
 * Returns: true if successful, false if failed
 */
bool initializeCamera() {
  Serial.println("📷 Initializing ESP32-CAM...");

  // Camera configuration structure
  camera_config_t config;
  config.ledc_channel = LEDC_CHANNEL_0;
  config.ledc_timer = LEDC_TIMER_0;
  config.pin_d0 = Y2_GPIO_NUM;
  config.pin_d1 = Y3_GPIO_NUM;
  config.pin_d2 = Y4_GPIO_NUM;
  config.pin_d3 = Y5_GPIO_NUM;
  config.pin_d4 = Y6_GPIO_NUM;
  config.pin_d5 = Y7_GPIO_NUM;
  config.pin_d6 = Y8_GPIO_NUM;
  config.pin_d7 = Y9_GPIO_NUM;
  config.pin_xclk = XCLK_GPIO_NUM;
  config.pin_pclk = PCLK_GPIO_NUM;
  config.pin_vsync = VSYNC_GPIO_NUM;
  config.pin_href = HREF_GPIO_NUM;
  config.pin_sscb_sda = SIOD_GPIO_NUM;
  config.pin_sscb_scl = SIOC_GPIO_NUM;
  config.pin_pwdn = PWDN_GPIO_NUM;
  config.pin_reset = RESET_GPIO_NUM;
  config.xclk_freq_hz = 20000000;
  config.pixel_format = PIXFORMAT_JPEG;

  // Configure quality based on PSRAM availability
  if (psramFound()) {
    config.frame_size = FRAMESIZE_UXGA;  // 1600x1200
    config.jpeg_quality = 10;            // High quality (lower number = better quality)
    config.fb_count = 2;                 // Frame buffer count
    Serial.println("📦 PSRAM detected - High quality mode enabled");
  } else {
    config.frame_size = FRAMESIZE_SVGA;  // 800x600
    config.jpeg_quality = 12;            // Standard quality
    config.fb_count = 1;                 // Single frame buffer
    Serial.println("⚠️ No PSRAM - Standard quality mode");
  }

  // Initialize camera with retry mechanism
  esp_err_t err = ESP_FAIL;
  for (int attempt = 1; attempt <= CAMERA_RETRY_COUNT; attempt++) {
    Serial.printf("🔄 Camera init attempt %d/%d...\n", attempt, CAMERA_RETRY_COUNT);
    err = esp_camera_init(&config);

    if (err == ESP_OK) {
      Serial.println("✅ Camera initialized successfully!");
      return true;
    } else {
      Serial.printf("❌ Camera init failed (0x%x), retrying...\n", err);
      delay(1000);
    }
  }

  Serial.println("💥 Camera initialization failed after all attempts!");
  return false;
}

/**
 * Photo capture and transmission system
 */
camera_fb_t* currentFrameBuffer = nullptr;
size_t currentIndex = 0;

// Helper function to check if more data is available for transmission
bool isMoreDataAvailable() {
  return currentFrameBuffer && (currentIndex < currentFrameBuffer->len);
}

// Helper function to get next byte for transmission
byte getNextByte() {
  if (currentFrameBuffer && currentIndex < currentFrameBuffer->len) {
    return currentFrameBuffer->buf[currentIndex++];
  }
  return 0;
}

/**
 * Capture photo and send to Telegram
 * Returns: Status message string
 */
String captureAndSendPhoto(String chatId) {
  Serial.println("📸 Starting photo capture...");

  // Ensure system stability
  yield();

  // Capture photo
  currentFrameBuffer = esp_camera_fb_get();
  currentIndex = 0;

  if (!currentFrameBuffer) {
    Serial.println("❌ Camera capture failed!");
    return "❌ **Camera Error**\n\n🚫 Failed to capture photo\n💡 Try again or check camera connection";
  }

  Serial.printf("📷 Photo captured successfully: %d bytes\n", currentFrameBuffer->len);

  // Flash LED during transmission
  digitalWrite(FLASH_LED_PIN, HIGH);

  // Send photo via Telegram
  yield(); // Feed watchdog
  bool success = bot.sendPhotoByBinary(
    chatId,
    "image/jpeg",
    currentFrameBuffer->len,
    isMoreDataAvailable,
    getNextByte,
    nullptr,
    nullptr
  );

  // Turn off flash
  digitalWrite(FLASH_LED_PIN, LOW);

  // Release frame buffer
  esp_camera_fb_return(currentFrameBuffer);
  currentFrameBuffer = nullptr;

  if (success) {
    totalPhotosSent++;
    Serial.println("✅ Photo sent successfully!");
    return "📸 **Photo Delivered!**\n\n✅ Image captured and sent successfully\n🎯 Total photos sent: " + String(totalPhotosSent);
  } else {
    Serial.println("❌ Failed to send photo");
    return "❌ **Transmission Failed**\n\n📷 Photo captured but failed to send\n📡 Check your internet connection";
  }
}

// ===========================================
// � MOTION DETECTION SYSTEM
// ===========================================

/**
 * Motion detection interrupt service routine
 * Called when PIR sensor detects motion
 */
void IRAM_ATTR motionDetectionISR() {
  unsigned long currentTime = millis();

  // Debounce check to prevent false triggers
  if (currentTime - lastMotionInterrupt > MOTION_DEBOUNCE_MS) {
    motionDetected = true;
    lastMotionInterrupt = currentTime;
  }
}

/**
 * Initialize motion detection system
 * Returns: true if successful, false if failed
 */
bool initializeMotionDetection() {
  Serial.println("🔔 Setting up motion detection...");

  // Configure PIR pin
  pinMode(PIR_PIN, INPUT);

  // Try to attach interrupt
  if (digitalPinToInterrupt(PIR_PIN) != -1) {
    // Ensure no existing interrupt is attached
    detachInterrupt(digitalPinToInterrupt(PIR_PIN));
    delay(100);

    // Attach new interrupt
    attachInterrupt(digitalPinToInterrupt(PIR_PIN), motionDetectionISR, RISING);
    motionInterruptAttached = true;
    Serial.println("✅ Motion interrupt attached successfully");
    return true;
  } else {
    Serial.println("⚠️ PIR pin doesn't support interrupts - using polling mode");
    motionInterruptAttached = false;
    return false;
  }
}

/**
 * Handle motion detection and alerts
 */
void handleMotionDetection() {
  if (!motionDetectionEnabled) return;

  bool currentMotion = false;

  // Check motion based on detection mode
  if (motionInterruptAttached) {
    currentMotion = motionDetected;
  } else {
    // Polling mode fallback
    currentMotion = (digitalRead(PIR_PIN) == HIGH);
  }

  if (currentMotion) {
    unsigned long currentTime = millis();

    // Check cooldown period to prevent spam
    if (currentTime - lastMotionTime > MOTION_COOLDOWN_MS) {
      lastMotionTime = currentTime;
      totalMotionEvents++;

      Serial.println("🚨 MOTION DETECTED! Initiating security protocol...");

      // Only proceed if WiFi is connected
      if (wifiConnected && telegramConnected) {
        // Send immediate alert
        String alertMessage = "🚨 **SECURITY ALERT** 🚨\n\n";
        alertMessage += "🕐 **Time:** `" + getSystemUptime() + "`\n";
        alertMessage += "📍 **Location:** ESP32-CAM Security System\n";
        alertMessage += "🔢 **Event #:** " + String(totalMotionEvents) + "\n";
        alertMessage += "📸 **Status:** Capturing photo...\n\n";
        alertMessage += "_🛡️ Automatic monitoring active_";

        bot.sendMessage(CHAT_ID, alertMessage, "Markdown");

        // Capture and send photo
        yield();
        String photoResult = captureAndSendPhoto(CHAT_ID);
        bot.sendMessage(CHAT_ID, photoResult, "Markdown");

      } else {
        Serial.println("⚠️ Network not available - motion logged but not transmitted");
      }
    }

    // Reset motion flag
    motionDetected = false;
  }
}

// ===========================================
// 🛠️ UTILITY FUNCTIONS
// ===========================================

/**
 * Get formatted system uptime
 */
String getSystemUptime() {
  unsigned long uptime = (millis() - systemStartTime) / 1000;
  unsigned long days = uptime / 86400;
  unsigned long hours = (uptime % 86400) / 3600;
  unsigned long minutes = (uptime % 3600) / 60;
  unsigned long seconds = uptime % 60;

  String timeStr = "";
  if (days > 0) timeStr += String(days) + "d ";
  if (hours > 0) timeStr += String(hours) + "h ";
  if (minutes > 0) timeStr += String(minutes) + "m ";
  timeStr += String(seconds) + "s";

  return timeStr;
}

/**
 * Get comprehensive WiFi information
 */
String getWiFiInformation() {
  String info = "🌐 **WiFi Network Information**\n\n";

  if (wifiConnected) {
    info += "📶 **SSID:** `" + WiFi.SSID() + "`\n";
    info += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
    info += "� **MAC Address:** `" + WiFi.macAddress() + "`\n";
    info += "� **Signal Strength:** `" + String(WiFi.RSSI()) + " dBm`\n";

    // Signal quality assessment
    int rssi = WiFi.RSSI();
    String quality;
    if (rssi > -30) quality = "Excellent 🟢";
    else if (rssi > -50) quality = "Very Good 🟡";
    else if (rssi > -70) quality = "Good 🟠";
    else if (rssi > -80) quality = "Fair 🔴";
    else quality = "Poor ⚫";

    info += "📊 **Signal Quality:** " + quality + "\n";
    info += "🔌 **Status:** Connected ✅";
  } else {
    info += "❌ **Status:** Disconnected\n";
    info += "🔄 **Action:** Attempting reconnection...";
  }

  return info;
}

/**
 * Get comprehensive system status
 */
String getSystemStatus() {
  String status = "📡 **System Status Report**\n\n";

  // System Information
  status += "🟢 **System:** " + String(systemInitialized ? "Online" : "Initializing") + "\n";
  status += "⏱ **Uptime:** `" + getSystemUptime() + "`\n";
  status += "💾 **Free Heap:** `" + String(ESP.getFreeHeap()) + " bytes`\n";
  status += "🧠 **PSRAM:** " + String(psramFound() ? "Available ✅" : "Not Found ❌") + "\n\n";

  // Network Status
  status += "📶 **WiFi:** " + String(wifiConnected ? "Connected ✅" : "Disconnected ❌") + "\n";
  if (wifiConnected) {
    status += "🌍 **IP:** `" + WiFi.localIP().toString() + "`\n";
    status += "📡 **RSSI:** `" + String(WiFi.RSSI()) + " dBm`\n";
  }
  status += "🤖 **Telegram:** " + String(telegramConnected ? "Connected ✅" : "Disconnected ❌") + "\n\n";

  // Security Features
  status += "🛰️ **Motion Detection:** " + String(motionDetectionEnabled ? "Enabled ✅" : "Disabled ❌") + "\n";
  status += "🔔 **Detection Mode:** " + String(motionInterruptAttached ? "Interrupt" : "Polling") + "\n";
  status += "💡 **Flash LED:** " + String(flashLedState ? "ON ✅" : "OFF ❌") + "\n";
  status += "🧠 **Face Detection:** " + String(faceDetectionEnabled ? "Enabled ✅" : "Disabled ❌") + "\n\n";

  // Statistics
  status += "📊 **Statistics:**\n";
  status += "🚨 Motion Events: `" + String(totalMotionEvents) + "`\n";
  status += "📸 Photos Sent: `" + String(totalPhotosSent) + "`\n";
  status += "⌨️ Commands Processed: `" + String(totalCommandsProcessed) + "`";

  return status;
}

// ===========================================
// 🎛️ TELEGRAM CUSTOM KEYBOARD & COMMANDS
// ===========================================

/**
 * Create custom reply keyboard for Telegram
 */
String createCustomKeyboard() {
  String keyboard = "[[\"📸 /photo\", \"💡 /flash\"],"
                   "[\"📡 /status\", \"⏱ /uptime\"],"
                   "[\"🛰️ /motionon\", \"🔕 /motionoff\"],"
                   "[\"🌐 /wifi\", \"🏓 /ping\"],"
                   "[\"🔁 /reboot\", \"🆘 /help\"]]";
  return keyboard;
}

/**
 * Send message with custom keyboard
 */
void sendMessageWithKeyboard(String chatId, String message) {
  String keyboard = createCustomKeyboard();
  bot.sendMessageWithReplyKeyboard(chatId, message, "Markdown", keyboard, true);
}

/**
 * Process incoming Telegram messages and commands
 */
void handleTelegramMessages() {
  int numNewMessages = bot.getUpdates(bot.last_message_received + 1);

  while (numNewMessages) {
    Serial.printf("📨 Processing %d new message(s)\n", numNewMessages);

    for (int i = 0; i < numNewMessages; i++) {
      String chatId = String(bot.messages[i].chat_id);
      String messageText = bot.messages[i].text;
      String fromName = bot.messages[i].from_name;

      // Security check - only respond to authorized chat ID
      if (chatId != CHAT_ID) {
        Serial.println("🚫 Unauthorized access attempt from: " + chatId);
        bot.sendMessage(chatId, "🚫 **Access Denied**\n\nUnauthorized user detected!\nThis incident has been logged.", "Markdown");
        continue;
      }

      totalCommandsProcessed++;
      Serial.printf("👤 From: %s | 💭 Command: %s\n", fromName.c_str(), messageText.c_str());

      // Process commands
      processCommand(chatId, messageText, fromName);
    }

    numNewMessages = bot.getUpdates(bot.last_message_received + 1);
  }
}

/**
 * Process individual commands
 */
void processCommand(String chatId, String command, String fromName) {
  // Remove any extra characters and normalize command
  command.trim();
  command.toLowerCase();

  if (command == "/start") {
    handleStartCommand(chatId, fromName);
  }
  else if (command == "/photo") {
    handlePhotoCommand(chatId);
  }
  else if (command == "/flash") {
    handleFlashCommand(chatId);
  }
  else if (command == "/status") {
    handleStatusCommand(chatId);
  }
  else if (command == "/reboot") {
    handleRebootCommand(chatId);
  }
  else if (command == "/ping") {
    handlePingCommand(chatId);
  }
  else if (command == "/motionon") {
    handleMotionOnCommand(chatId);
  }
  else if (command == "/motionoff") {
    handleMotionOffCommand(chatId);
  }
  else if (command == "/uptime") {
    handleUptimeCommand(chatId);
  }
  else if (command == "/wifi") {
    handleWiFiCommand(chatId);
  }
  else if (command == "/help") {
    handleHelpCommand(chatId);
  }
  else if (command == "/faceon") {
    handleFaceOnCommand(chatId);
  }
  else if (command == "/faceoff") {
    handleFaceOffCommand(chatId);
  }
  else {
    handleUnknownCommand(chatId, command);
  }
}

// ===========================================
// 🎯 INDIVIDUAL COMMAND HANDLERS
// ===========================================

/**
 * Handle /start command - Welcome message with keyboard
 */
void handleStartCommand(String chatId, String fromName) {
  String welcome = "🏁 **ESP32-CAM Security System**\n\n";
  welcome += "Welcome *" + fromName + "*! 👋\n\n";
  welcome += "🛡️ Your smart security system is **ONLINE** and ready!\n\n";
  welcome += "📋 **Quick Access Menu:**\n";
  welcome += "Use the buttons below for instant control! ⚡\n\n";
  welcome += "🔒 **System Status:**\n";
  welcome += "• Status: ✅ Active\n";
  welcome += "• Connection: ✅ Stable\n";
  welcome += "• Motion Detection: " + String(motionDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
  welcome += "• Uptime: `" + getSystemUptime() + "`\n\n";
  welcome += "_Your security is our priority!_ 🛡️";

  sendMessageWithKeyboard(chatId, welcome);
}

/**
 * Handle /photo command - Capture and send photo
 */
void handlePhotoCommand(String chatId) {
  bot.sendMessage(chatId, "📸 **Capturing Photo...**\n\nPlease wait while I take a picture! 📷✨", "Markdown");
  String result = captureAndSendPhoto(chatId);
  bot.sendMessage(chatId, result, "Markdown");
}
        bot.sendMessage(chat_id, result, "Markdown");
      }
      
      else if (text == "/flash") {
        flashState = !flashState;
        digitalWrite(FLASH_LED_PIN, flashState ? HIGH : LOW);
        String msg = flashState ? "💡 *Flash LED ON*\nFlash light activated! ✨" : "🌙 *Flash LED OFF*\nFlash light deactivated! 💤";
        bot.sendMessage(chat_id, msg, "Markdown");
      }
      
      else if (text == "/status") {
        String status = "📡 **System Status Report**\n\n";
        status += "🟢 **System:** Online & Operational\n";
        status += "🌍 **IP Address:** `" + WiFi.localIP().toString() + "`\n";
        status += "📶 **WiFi RSSI:** `" + String(WiFi.RSSI()) + " dBm`\n";
        status += "⏱ **Uptime:** `" + getFormattedTime() + "`\n";
        status += "🛰️ **Motion Detection:** " + String(motionDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
        status += "💡 **Flash LED:** " + String(flashState ? "✅ ON" : "❌ OFF") + "\n";
        status += "🧠 **Face Detection:** " + String(faceDetectionEnabled ? "✅ Enabled" : "❌ Disabled") + "\n";
        status += "💾 **Free Heap:** `" + String(ESP.getFreeHeap()) + " bytes`\n";
        status += "🔋 **PSRAM:** " + String(psramFound() ? "✅ Available" : "❌ Not Found");
        
        bot.sendMessage(chat_id, status, "Markdown");
      }
      
      else if (text == "/reboot") {
        bot.sendMessage(chat_id, "🔁 *Rebooting System...*\nESP32-CAM will restart now! ⚡\n\n_Please wait 30 seconds before reconnecting_ ⏳", "Markdown");
        delay(1000);
        ESP.restart();
      }
      
      else if (text == "/ping") {
        bot.sendMessage(chat_id, "🏓 **PONG!** 🎾\n\n⚡ *Response Time:* Quick!\n✅ *Status:* System Responsive\n📶 *Connection:* Stable", "Markdown");
      }
      
      else if (text == "/motionon") {
        motionDetectionEnabled = true;
        bot.sendMessage(chat_id, "🛰️ **Motion Detection ENABLED**\n\n✅ System will now monitor for motion\n🚨 Alerts will be sent automatically\n📸 Photos will be captured on detection\n\n_Stay secure!_ 🛡️", "Markdown");
      }
      
      else if (text == "/motionoff") {
        motionDetectionEnabled = false;
        bot.sendMessage(chat_id, "🔕 **Motion Detection DISABLED**\n\n❌ Motion monitoring stopped\n😴 System in quiet mode\n📵 No automatic alerts\n\n_Manual control only_ 🎛️", "Markdown");
      }
      
      else if (text == "/uptime") {
        String uptime = "⏱ **System Uptime**\n\n";
        uptime += "🕐 **Running for:** `" + getFormattedTime() + "`\n";
        uptime += "🔄 **Started:** " + String((millis() - (millis() % 1000)) / 1000) + " seconds ago\n";
        uptime += "⚡ **Status:** Stable & Operational\n\n";
        uptime += "_System has been running smoothly!_ ✨";
        
        bot.sendMessage(chat_id, uptime, "Markdown");
      }
      
      else if (text == "/wifi") {
        bot.sendMessage(chat_id, getWiFiInfo(), "Markdown");
      }
      
      else if (text == "/help") {
        String help = "🆘 **Command Reference Guide**\n\n";
        help += "📋 **Available Commands:**\n\n";
        help += "🏁 `/start` - *Welcome & status overview*\n";
        help += "📸 `/photo` - *Capture & send photo*\n";
        help += "💡 `/flash` - *Toggle flash LED*\n";
        help += "📡 `/status` - *Detailed system status*\n";
        help += "🔁 `/reboot` - *Restart the system*\n";
        help += "🏓 `/ping` - *Test connection*\n";
        help += "🛰️ `/motionon` - *Enable motion alerts*\n";
        help += "🔕 `/motionoff` - *Disable motion alerts*\n";
        help += "⏱ `/uptime` - *Show system uptime*\n";
        help += "🌐 `/wifi` - *WiFi connection info*\n";
        help += "🧠 `/faceon` - *Enable face detection*\n";
        help += "🚫 `/faceoff` - *Disable face detection*\n\n";
        help += "💡 **Quick Tip:** Commands are case-sensitive! ⚡";
        
        bot.sendMessage(chat_id, help, "Markdown");
      }
      
      else if (text == "/faceon") {
        faceDetectionEnabled = true;
        bot.sendMessage(chat_id, "🧠 **Face Detection ENABLED**\n\n✅ AI face recognition active\n👤 Will detect human faces\n🎯 Enhanced security mode\n\n_Smart monitoring activated!_ 🤖", "Markdown");
      }
      
      else if (text == "/faceoff") {
        faceDetectionEnabled = false;
        bot.sendMessage(chat_id, "🚫 **Face Detection DISABLED**\n\n❌ Face recognition stopped\n📷 Basic camera mode only\n⚡ Reduced processing load\n\n_Standard monitoring mode_ 📸", "Markdown");
      }
      
      else {
        String unknown = "❓ **Unknown Command**\n\n";
        unknown += "Command `" + text + "` not recognized.\n\n";
        unknown += "💡 **Try these instead:**\n";
        unknown += "• Type `/help` for all commands\n";
        unknown += "• Type `/start` to begin\n";
        unknown += "• Type `/status` for system info\n\n";
        unknown += "_I'm here to help!_ 🤖";
        
        bot.sendMessage(chat_id, unknown, "Markdown");
      }
    }
    
    numNewMessages = bot.getUpdates(bot.last_message_received + 1);
  }
}

// ===========================================
// 🚀 SETUP FUNCTION
// ===========================================
void setup() {
  Serial.begin(115200);
  delay(1000);  // Give serial time to initialize
  Serial.println();
  Serial.println("🚀 ESP32-CAM Security System Starting...");

  // Record start time
  startTime = millis();

  // Initialize pins
  pinMode(PIR_PIN, INPUT);
  pinMode(FLASH_LED_PIN, OUTPUT);
  digitalWrite(FLASH_LED_PIN, LOW);

  // Setup motion detection interrupt with proper error handling
  Serial.println("🔔 Configuring motion detection...");
  if (digitalPinToInterrupt(PIR_PIN) != -1) {
    // Detach any existing interrupt first to prevent "already installed" error
    detachInterrupt(digitalPinToInterrupt(PIR_PIN));
    delay(100);  // Small delay to ensure cleanup

    // Now attach the interrupt
    attachInterrupt(digitalPinToInterrupt(PIR_PIN), motionISR, RISING);
    interruptAttached = true;
    Serial.println("✅ Motion detection interrupt configured successfully");
  } else {
    Serial.println("⚠️ PIR pin doesn't support interrupts, using polling method");
    interruptAttached = false;
  }
  
  // Initialize camera
  Serial.println("📷 Initializing camera...");
  if (!initCamera()) {
    Serial.println("❌ Camera initialization failed!");
    Serial.println("🔄 System will restart in 5 seconds...");
    delay(5000);
    ESP.restart();
  }

  // Connect to WiFi with improved error handling
  Serial.println("📡 Connecting to WiFi...");
  WiFi.mode(WIFI_STA);  // Set WiFi to station mode
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

  unsigned long wifiStartTime = millis();
  int attempts = 0;

  while (WiFi.status() != WL_CONNECTED && (millis() - wifiStartTime) < WIFI_TIMEOUT_MS) {
    delay(500);
    Serial.print(".");
    attempts++;

    // Feed watchdog during WiFi connection
    yield();

    // Try to reconnect every 10 attempts
    if (attempts % 10 == 0) {
      Serial.println();
      Serial.printf("🔄 Attempt %d - Retrying WiFi connection...\n", attempts);
      WiFi.disconnect();
      delay(1000);
      WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
    }
  }

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("\n✅ WiFi connected successfully!");
    Serial.printf("🌍 IP Address: %s\n", WiFi.localIP().toString().c_str());
    Serial.printf("📶 Signal Strength: %d dBm\n", WiFi.RSSI());
    Serial.printf("🔗 MAC Address: %s\n", WiFi.macAddress().c_str());
  } else {
    Serial.println("\n❌ WiFi connection failed after timeout!");
    Serial.println("🔄 System will restart in 5 seconds...");
    delay(5000);
    ESP.restart();
  }
  
  // Configure Telegram secure client
  Serial.println("🤖 Configuring Telegram client...");
  clientTCP.setInsecure(); // Use this for newer ESP32 core versions

  // Test Telegram connection with retry logic
  Serial.println("🔗 Testing Telegram connection...");
  bool botConnected = false;
  int telegramAttempts = 0;

  while (!botConnected && telegramAttempts < 3) {
    telegramAttempts++;
    Serial.printf("🔄 Telegram attempt %d/3...\n", telegramAttempts);

    botConnected = bot.getMe();
    if (!botConnected) {
      Serial.println("⚠️ Telegram connection failed, retrying...");
      delay(2000);
    }
    yield(); // Feed watchdog
  }

  if (botConnected) {
    Serial.println("✅ Telegram bot connected successfully!");

    // Send startup notification
    String startupMsg = "🚀 **System Online!**\n\n";
    startupMsg += "✅ ESP32-CAM Security System started\n";
    startupMsg += "🌍 IP: `" + WiFi.localIP().toString() + "`\n";
    startupMsg += "📶 WiFi: `" + String(WiFi.RSSI()) + " dBm`\n";
    startupMsg += "🛰️ Motion Detection: " + String(interruptAttached ? "✅ Interrupt" : "⚠️ Polling") + "\n\n";
    startupMsg += "_Your security system is ready!_ 🛡️";

    if (!bot.sendMessage(CHAT_ID, startupMsg, "Markdown")) {
      Serial.println("⚠️ Failed to send startup notification");
    }
  } else {
    Serial.println("❌ Telegram connection failed after all attempts!");
    Serial.println("⚠️ System will continue without Telegram notifications");
  }

  Serial.println("🎉 Setup completed successfully!");
  Serial.println("🔒 Security system is now ACTIVE and monitoring...");
  Serial.printf("💾 Free heap: %d bytes\n", ESP.getFreeHeap());
}

// ===========================================
// 🔄 MAIN LOOP
// ===========================================
void loop() {
  // Feed watchdog timer to prevent resets
  yield();

  // Check WiFi connection and reconnect if needed
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("⚠️ WiFi disconnected, attempting reconnection...");
    WiFi.reconnect();
    delay(1000);
  }

  // Handle motion detection
  handleMotionDetection();

  // Handle Telegram messages (with timing control)
  if (millis() > lastTimeBotRan + botRequestDelay) {
    handleTelegramMessages();
    lastTimeBotRan = millis();
  }

  // Watchdog feed and small delay
  yield();
  delay(100); // Balanced delay to reduce load while maintaining responsiveness
}